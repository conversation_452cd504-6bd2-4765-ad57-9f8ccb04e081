import * as d3 from "d3";
import type { SVGCommand } from "svg-pathdata/dist/types";

import { useCommonStore, useDataStore } from "@/stores";
import type { INode, IPosition } from "@/types";
import { updateLinkByHttp } from "@/utils/http/apis";

import { getAdsorbNode, getAdsorbPoint, makeRightAngle } from "../../helper";

let adsorbNode: INode | undefined;
let pointIndex = 0;

const dragStart = (e: any, el: SVGCircleElement) => {
  pointIndex = Number(el.getAttribute("data-index"));
};

// const getAdsorbNodeByThrottle = throttle({ interval: 50 }, getAdsorbNode);

const dragging = (e: any, el: SVGCircleElement) => {
  const dataStore = useDataStore();
  const commonStore = useCommonStore();

  let x = e.x;
  let y = e.y;
  const point = dataStore.linksSelected[0].points[pointIndex];

  if (pointIndex === 0 || pointIndex === dataStore.linksSelected[0].points.length - 1) {
    getAdsorbNode({ x: e.x, y: e.y }, (node) => {
      adsorbNode = node;
      if (node) {
        const p = getAdsorbPoint(node, { x: e.x, y: e.y });
        // 小于 5px 吸附
        if (Math.abs(e.x - p.x) < 8 && Math.abs(e.y - p.y) < 8) {
          x = p.x;
          y = p.y;
        }
      }
    });
  } else {
    if (commonStore.isShiftDown) {
      const res = makeRightAngle(
        dataStore.linksSelected[0].points[pointIndex - 1] as IPosition,
        dataStore.linksSelected[0].points[pointIndex] as IPosition,
        dataStore.linksSelected[0].points[pointIndex + 1] as IPosition
      );
      x = res.x;
      y = res.y;
    }
  }

  if ("x" in point) {
    point.x = x;
  }
  if ("y" in point) {
    point.y = y;
  }
  d3.select(el).attr("cx", x).attr("cy", y);
};

const dragEnd = async () => {
  const dataStore = useDataStore();

  if (adsorbNode) {
    if (pointIndex === 0) {
      dataStore.linksSelected[0].source = adsorbNode;
      dataStore.linksSelected[0].fromObj = adsorbNode.nodeId;
    } else if (pointIndex === dataStore.linksSelected[0].points.length - 1) {
      dataStore.linksSelected[0].target = adsorbNode;
      dataStore.linksSelected[0].endObj = adsorbNode.nodeId;
    }
  }
  adsorbNode = undefined;
  updateLinkByHttp([dataStore.linksSelected[0]]);
};

export const bindLinkDragPointEvent = (points: SVGCommand[]) => {
  const drag = d3
    .drag<SVGCircleElement, any>()
    .on("start", function (e) {
      dragStart(e, this);
    })
    .on("drag", function (e) {
      dragging(e, this);
    })
    .on("end", dragEnd);

  d3.select<SVGElement, any>("#linkDragPointGroup")
    .data(points)
    .selectAll<SVGCircleElement, any>("circle")
    .style("cursor", "move")
    .each(function () {
      d3.select(this).call(drag);
    });
};
