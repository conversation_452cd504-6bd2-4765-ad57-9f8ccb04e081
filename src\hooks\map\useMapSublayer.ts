import { watchEffect } from "vue";
import { throttle } from "radash";

import { useMapStore, useSvgStore } from "@/stores";
import { renewNodesLinks } from "@/utils/tools";

export const useMapSublayer = () => {
  const mapStore = useMapStore();
  const svgStore = useSvgStore();

  const getDisplayLayers = (scale: number) => {
    console.log("mapStore.sublayers", mapStore.sublayers);

    return mapStore.sublayers
      .filter((sublayer) => {
        if (Number(sublayer.scaleMin) === 0 && Number(sublayer.scaleMax) === 0) {
          return true;
        }
        // 如果最小缩放比例为0，判断最大缩放比例，最大在缩放比例内，返回true
        if (Number(sublayer.scaleMax) === 0) {
          return Number(sublayer.scaleMin) <= scale;
        }
        return scale >= Number(sublayer.scaleMin) && scale <= Number(sublayer.scaleMax);
      })
      .map((sublayer) => sublayer.sublayerId);
  };

  const renewNodesLinksBythrottle = throttle({ interval: 200 }, () => {
    renewNodesLinks();
    console.log("renewNodesLinks", Date.now());
  });

  watchEffect(() => {
    if (!mapStore.isScaleDisplayStatus) return;
    // mapStore.sublayerIds
    // mapStore.sublayers
    mapStore.sublayerIds = getDisplayLayers(svgStore.scale * 100);
    renewNodesLinksBythrottle();
  });
  return {};
};
