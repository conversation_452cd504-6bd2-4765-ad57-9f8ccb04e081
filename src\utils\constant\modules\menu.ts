import type { ISelectType } from "@/types";

export const AlignMenu = [
  {
    label: "对齐",
    key: "Align",
    children: [
      {
        label: "左对齐",
        key: "Left",
        parent: "<PERSON>gn"
      },
      {
        label: "右对齐",
        key: "Right",
        parent: "<PERSON>gn"
      },
      {
        label: "顶对齐",
        key: "Top",
        parent: "<PERSON>gn"
      },
      {
        label: "底对齐",
        key: "Bottom",
        parent: "<PERSON>gn"
      },
      {
        label: "水平居中",
        key: "CenterX",
        parent: "<PERSON>gn"
      },
      {
        label: "垂直居中",
        key: "CenterY",
        parent: "Align"
      },
      {
        label: "水平等距",
        key: "DistributeX",
        parent: "<PERSON>gn"
      },
      {
        label: "垂直等距",
        key: "DistributeY",
        parent: "Align"
      }
    ]
  }
];
const CommonMenu = [
  {
    label: "复制",
    key: "Clone"
  },
  {
    label: "粘贴",
    key: "Paste"
  },
  {
    label: "子图层",
    key: "Sublayer",
    children: [
      {
        label: "添加",
        key: "UpdateSublayer"
      },
      {
        label: "移除",
        key: "RemoveMultiFromSublayer"
      }
    ]
  },
  {
    label: "删除",
    key: "Delete"
  }
];

const GroupMenu = [
  {
    label: "锁定",
    key: "Lock"
  },
  {
    label: "分组",
    key: "AddGroup"
  }
];

export const EditMenu: Record<ISelectType, any> = {
  node: [...AlignMenu, ...CommonMenu],
  link: [...AlignMenu, ...CommonMenu],
  svg: [...AlignMenu, ...CommonMenu],
  group: [...AlignMenu, ...GroupMenu, ...CommonMenu]
};
