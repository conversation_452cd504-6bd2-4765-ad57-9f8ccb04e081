<template>
  <n-modal
    v-model:show="isVisible"
    preset="dialog"
    title="导出图元配置"
    size="huge"
    :bordered="false"
    :show-icon="false"
    :close-on-esc="false"
    :maskClosable="false"
    positive-text="导出"
    negative-text="取消"
    @positive-click="handleExport"
    @negative-click="hide"
    style="margin-top: 20vh"
  >
    <div>
      <n-form
        ref="formRef"
        :model="formValue"
        :rules="rules"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="文件名称" path="fileName">
          <n-input v-model:value="formValue.fileName" placeholder="请输入导出文件名称" />
        </n-form-item>

        <n-form-item label="导出内容" path="exportType">
          <n-radio-group v-model:value="formValue.exportType">
            <n-space>
              <n-radio :value="'all'">全部导出</n-radio>
              <n-radio :value="'selected'">选择导出</n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item>

        <n-form-item
          v-if="formValue.exportType === 'selected'"
          label="选择分组"
          path="selectedGroups"
        >
          <n-tree
            checkable
            :data="treeOptions"
            :checked-keys="formValue.selectedGroups"
            :selectable="false"
            style="max-height: 400px; overflow-y: auto"
            @update:checked-keys="handleCheckedKeysChange"
          />
        </n-form-item>
      </n-form>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import type { FormInst, FormRules, TreeOption } from "naive-ui";

import { useMetaStore } from "@/stores";

type ExportType = "all" | "selected";

interface FormState {
  fileName: string;
  exportType: ExportType;
  selectedGroups: string[];
}

const metaStore = useMetaStore();
const isVisible = ref(false);
const formRef = ref<FormInst | null>(null);

const formValue = ref<FormState>({
  fileName: "图元配置导出",
  exportType: "all",
  selectedGroups: []
});

const rules: FormRules = {
  fileName: {
    required: true,
    message: "请输入文件名称",
    trigger: ["input", "blur"]
  }
};

// 树形结构的选项
const treeOptions = computed<TreeOption[]>(() => {
  // 深拷贝避免修改原始数据
  return JSON.parse(JSON.stringify(metaStore.metaGroupData));
});

const show = () => {
  isVisible.value = true;
  // 重置表单
  formValue.value = {
    fileName: "图元配置导出",
    exportType: "all",
    selectedGroups: []
  };
};

const hide = () => {
  isVisible.value = false;
};

const handleCheckedKeysChange = (keys: string[]) => {
  formValue.value.selectedGroups = keys;
};

const handleExport = () => {
  formRef.value?.validate(async (errors) => {
    if (errors) {
      return;
    }

    try {
      let exportData;

      if (formValue.value.exportType === "all") {
        // 导出全部图元数据
        exportData = metaStore.metaList;
      } else {
        // 导出选择的图元数据
        const selectedGroups = formValue.value.selectedGroups;

        // 过滤出选中的分组和图元
        exportData = metaStore.metaList
          .filter((group) => {
            // 如果分组被选中，直接包含整个分组
            if (selectedGroups.includes(group.groupId)) {
              return true;
            }

            // 如果部分图元被选中，则包含这些图元
            const hasSelectedObj = group.objList.some((obj) =>
              selectedGroups.includes(obj.objType)
            );

            return hasSelectedObj;
          })
          .map((group) => {
            // 如果分组被选中，直接返回整个分组
            if (selectedGroups.includes(group.groupId)) {
              return group;
            }

            // 否则，只返回选中的图元
            return {
              ...group,
              objList: group.objList.filter((obj) => selectedGroups.includes(obj.objType))
            };
          });
      }

      // 创建导出内容
      const exportContent = JSON.stringify(exportData, null, 2);

      // 创建Blob
      const blob = new Blob([exportContent], { type: "application/json" });

      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `${formValue.value.fileName}.json`;

      // 触发下载
      document.body.appendChild(link);
      link.click();

      // 清理
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      window.$message.success("导出成功");
      hide();
    } catch (error) {
      console.error("导出失败:", error);
      window.$message.error("导出失败");
    }
  });
};

defineExpose({
  show
});
</script>

<style scoped></style>
