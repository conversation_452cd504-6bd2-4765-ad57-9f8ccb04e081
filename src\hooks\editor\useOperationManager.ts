import { watch } from "vue";

import { useDataStore, useMapStore } from "@/stores";
import type { ILink } from "@/types";
import { operationManager } from "@/utils/editor/data/operationManager";

const getBindedLinks = () => {
  const dataStore = useDataStore();
  const links = new Set<ILink>();
  dataStore.nodesSelected.forEach((node) => {
    node.targets.forEach((link) => {
      links.add(link);
    });
    node.sources.forEach((link) => {
      links.add(link);
    });
  });
  return Array.from(links);
};

export const useOperationManager = () => {
  const mapStore = useMapStore();
  // const dataStore = useDataStore();

  watch(
    () => mapStore.mapInfo,
    () => {
      operationManager.clearData();
    }
  );

  // const nodeIdsSelected = computed(() => {
  //   return dataStore.nodesSelected.map((node) => node.nodeId);
  // });
  // const linkIdsSelected = computed(() => {
  //   return dataStore.linksSelected.map((link) => link.linkId);
  // });
  // watch(
  //   () => [dataStore.nodesSelected, dataStore.linksSelected],
  //   () => {
  //     console.log("=-==");

  //     operationManager.preSelect({
  //       nodes: dataStore.nodesSelected,
  //       links: Array.from(new Set([...dataStore.linksSelected, ...getBindedLinks()]))
  //     });
  //     // operationManager.preSelect({
  //     //   nodes: nodesSelected,
  //     //   links: linksSelected
  //     // });
  //   }
  // );

  return {};
};
