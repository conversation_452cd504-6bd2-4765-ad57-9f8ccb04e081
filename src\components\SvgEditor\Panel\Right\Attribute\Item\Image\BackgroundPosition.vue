<template>
  <BaseGrid title="背景位置">
    <n-select
      :value="value"
      filterable
      placeholder="选择位置"
      size="small"
      :options="BackgroundPositionOptions"
      @update:value="onChange"
    />
  </BaseGrid>
</template>

<script setup lang="ts">
import BaseGrid from "@/components/Common/BaseGrid/index.vue";

defineProps<{
  value?: string | number | null;
}>();

const emit = defineEmits<{
  "update:value": [value: string];
}>();

const BackgroundPositionOptions = [
  {
    label: "center",
    value: "center"
  },
  {
    label: "top",
    value: "top"
  },
  {
    label: "bottom",
    value: "bottom"
  },
  {
    label: "left",
    value: "left"
  },
  {
    label: "right",
    value: "right"
  }
];

const onChange = (value: string) => {
  emit("update:value", value);
};
</script>

<style scoped></style>
