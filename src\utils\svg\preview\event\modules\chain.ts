import * as d3 from "d3";

import { sendSyncMessage } from "@/utils/sync";

import { closePreviewChain } from "../../draw";

const zooming = (e: d3.D3ZoomEvent<SVGSVGElement, any>) => {
  sendSyncMessage("onZoomPreviewChainModal", { type: "modal", data: e.transform });

  d3.select("#chainPreviewGroup").attr(
    "transform",
    `translate(${e.transform.x},${e.transform.y}) scale(${e.transform.k})`
  );
};

const initLayoutPosition = (width: number, height: number) => {
  const layoutWidth = (document.querySelector("#previewLayout")?.clientWidth || 1600) - 40;
  const layoutHeight = (document.querySelector("#previewLayout")?.clientHeight || 900) - 40;

  const k = Math.min(layoutWidth / width, layoutHeight / height);
  const x = (layoutWidth - width * k) / 2;
  const y = (layoutHeight - height * k) / 2;
  return { x, y, k };
};

export const bindPreviewChainZoom = (layoutWidth: number, layoutHeight: number) => {
  const { x, y, k } = initLayoutPosition(layoutWidth, layoutHeight);
  const zoom = d3
    .zoom<SVGSVGElement, unknown>()
    .scaleExtent([0.01, 10])
    // .on("start", zoomstart)
    .on("zoom", zooming);

  d3.select<SVGSVGElement, any>("#previewChain")
    .call(zoom)
    .on("dblclick.zoom", null)
    .on("dblclick", function (e) {
      if (e.target === this) {
        closePreviewChain();
        sendSyncMessage("onHidePreviewChainModal", { type: "svg" });
      }
    })
    .on("click", function (e) {
      //   if (e.target === this) {
      //     d3.select("#chainPreviewGroup").remove();
      //     d3.select(this).style("display", "none");
      //   }
    })
    .call(zoom.transform, d3.zoomIdentity.translate(x + 20, y + 20).scale(k));
};
