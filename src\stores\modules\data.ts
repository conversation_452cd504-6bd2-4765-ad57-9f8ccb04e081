import { computed, ref } from "vue";
import { defineStore } from "pinia";

import type { IGroupData, ILink, INode, ISourceLink, ISourceNode } from "@/types/";

export const useDataStore = defineStore("data", () => {
  const nodesAll = ref<INode[]>([]);
  const linksAll = ref<ILink[]>([]);

  const nodes = ref<INode[]>([]);
  const links = ref<ILink[]>([]);

  const groups = ref<IGroupData[]>([]);

  const currentNode = ref<INode | null>(null);
  const currentLink = ref<ILink | null>(null);
  const isSelectionRectVisible = false;

  const previewNode = ref<INode | null>(null);
  const previewLink = ref<ILink | null>(null);

  const alignBaseNode = ref<INode | null>(null);

  const nodeLinkListByImport = ref<{
    nodeList: ISourceNode[];
    linkList: ISourceLink[];
  }>({
    nodeList: [],
    linkList: []
  });

  const nodesSelected = computed(() => nodes.value.filter((node) => node.selected));
  const linksSelected = computed(() => links.value.filter((link) => link.selected));
  const groupSelected = computed(() => groups.value.filter((group) => group.selected));

  const clearAlignBaseNode = () => {
    alignBaseNode.value = null;
  };

  const domIdOptions = computed(() => {
    const domIds = new Set<string>();
    nodesAll.value.forEach((node) => {
      if (node.domId) {
        domIds.add(node.domId);
      }
    });
    linksAll.value.forEach((link) => {
      if (link.domId) {
        domIds.add(link.domId);
      }
    });
    return Array.from(domIds).map((domId) => ({
      key: domId,
      value: domId
    }));
  });

  return {
    nodesAll,
    linksAll,
    nodeLinkListByImport,
    nodes,
    links,
    groups,
    currentNode,
    currentLink,
    previewNode,
    previewLink,
    alignBaseNode,
    clearAlignBaseNode,
    isSelectionRectVisible,
    nodesSelected,
    linksSelected,
    groupSelected,
    domIdOptions
  };
});
