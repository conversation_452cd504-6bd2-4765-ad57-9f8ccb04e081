<template>
  <BaseGrid title="背景地址">
    <n-input :value="value" placeholder="背景图片地址" size="small" @update:value="onChange" />
  </BaseGrid>
</template>

<script setup lang="ts">
import BaseGrid from "@/components/Common/BaseGrid/index.vue";

defineProps<{
  value?: string | number | null;
}>();

const emit = defineEmits<{
  "update:value": [value: string];
}>();

const onChange = (value: string) => {
  emit("update:value", value);
};
</script>

<style scoped></style>
