export interface IMapEventSource extends IMapEventItem {
  id?: number;
  createTime?: string;
  updateTime?: string;
  createBy?: string;
  updateBy?: string;
}

export interface IMapEventItem {
  mapId: string;
  eventType: string;
  name: string;
  trigger: IFilterTriggerItem[];
}

export interface IMapEventModel {
  id?: number;
  mapId?: string;
  eventType?: string;
  name?: string;
  trigger?: IFilterTriggerItem[];
  status?: boolean;
}

export interface IFilterTriggerItem {
  domId: string;
  detailId: string;
  objType: string;
  conditions: ITriggerObject[];
}

export type ITriggerObject = {
  name: string | null;
  key: string | null;
  value: string | null;
};

export interface IMapObjItem {
  objName: string;
  objType: string;
}

export interface ICommand {
  action: any[];
  cmdName: string;
}

export interface IInteractionTrigger {
  // key: string;
  // value: string;
  action: string | null;
  threshold: number;
  commands: ICommand[];
}

export interface IInteractionSource {
  id: number;
  mapId: string;
  eventType: string;
  name: string;
  trigger: IInteractionTrigger[];
  createTime?: string;
  updateTime?: string;
  createBy?: string;
  updateBy?: string;
}

export interface IInteractionModel extends Partial<IInteractionSource> {
  trigger: IInteractionTrigger[];
}
