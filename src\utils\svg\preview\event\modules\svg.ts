import * as d3 from "d3";

import { useMapStore, usePreviewStore } from "@/stores";
import type { INode, ISVG, ISVGG } from "@/types";
import { resetSvgSizePosition } from "@/utils/svg/common";
import { sendSyncMessage } from "@/utils/sync";

import { highlightPreview } from "../../attr";

let previewSvg: ISVG;
let previewGroup: ISVGG<SVGGElement, any>;
let zoom: d3.ZoomBehavior<SVGSVGElement, unknown>;
const marginWidth = 0;

const getInitialPreviewPositionSize = (scale?: number) => {
  const mapStore = useMapStore();
  const size = mapStore.mapSize;
  const width = window.innerWidth;
  const height = window.innerHeight;
  // 缩放比例 先判断宽高，然后计算缩放比例
  //   如果宽度大于高度，以高度为基准，否则以宽度为基准 在原来的基础上加上20px的padding
  let scaleFactor = Math.min(
    (width - marginWidth) / size.width,
    (height - marginWidth) / size.height
  );

  if (scaleFactor > 1) {
    scaleFactor = 1;
  }
  if (scale) {
    scaleFactor = scale;
  }

  const scaledWidth = size.width * scaleFactor;
  const scaledHeight = size.height * scaleFactor;

  const x = (width - scaledWidth) / 2;
  const y = (height - scaledHeight) / 2;

  return { x, y, width: size.width, height: size.height, k: scaleFactor };
};

const zooming = (e: d3.D3ZoomEvent<SVGSVGElement, any>) => {
  sendSyncMessage("onZoomPreviewSvg", { type: "svg", data: e.transform });
  previewGroup.attr(
    "transform",
    `translate(${e.transform.x},${e.transform.y}) scale(${e.transform.k})`
  );
};

export const bindPreviewSvgZoom = (svgView: ISVG) => {
  const previewStore = usePreviewStore();
  const { x, y, k } = getInitialPreviewPositionSize();

  previewSvg = svgView;
  previewGroup = previewSvg.select<SVGGElement>("#previewGroup");

  zoom = d3
    .zoom<SVGSVGElement, unknown>()
    .scaleExtent([0.01, 10])
    // .on("start", zoomstart)
    .on("zoom", zooming);
  // .on("end", zoomend);

  previewSvg
    .call(zoom)
    .on("dblclick.zoom", null)
    .on("click", function (e) {
      if (e.target === this || d3.select(e.target).attr("id") === "previewChainBg") {
        highlightPreview();
        previewStore.isMenuVisible = false;
        sendSyncMessage("onCnacelSelectNode", { type: "svg" });
      }
    })
    .call(zoom.transform, d3.zoomIdentity.translate(x, y).scale(k));
};

export const resetPreviewSvgSizePosition = () => {
  resetSvgSizePosition("previewSvg", zoom, getInitialPreviewPositionSize());
};

export const positionPreview = (node: INode) => {
  const { x, y, k, width, height } = getInitialPreviewPositionSize(1);
  const tx = x - node.x * k + marginWidth + (width * k) / 2 - (node.width * k) / 2;
  const ty = y - node.y * k + marginWidth + (height * k) / 2 - (node.height * k) / 2;

  d3.selectAll<SVGGElement, INode>(".preview-node-group").classed("highlight-node-link", false);
  d3.select<SVGSVGElement, any>(`#previewNode_${node.nodeId}`).classed("highlight-node-link", true);

  resetSvgSizePosition("previewSvg", zoom, { x: tx, y: ty, k });
};
