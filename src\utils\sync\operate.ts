import * as d3 from "d3";

import { useDataStore, usePreviewStore } from "@/stores";

import emitter from "../mitt";
import { highlightPreview } from "../svg/preview/attr";
import { closePreviewChain, drawPreviewChain } from "../svg/preview/draw";
import { getChainNodesByNode } from "../svg/preview/helper";

export const operate = (data: { event: string; payload: Record<string, unknown> }) => {
  console.log("operate", data);
  switch (data.event) {
    case "onSelectNode":
      onSelectNode(data.payload);
      break;
    case "onCnacelSelectNode":
      onCnacelSelectNode();
      break;
    case "onShowPreviewChainModal":
      onShowPreviewChainModal(data.payload);
      break;
    case "onHidePreviewChainModal":
      closePreviewChain();
      break;
    case "onZoomPreviewChainModal":
      onZoomPreviewChainModal(data.payload);
      break;
    case "onZoomPreviewSvg":
      onZoomPreviewSvg(data.payload);
      break;
    case "onMapChange":
      onMapChange(data.payload);
      break;
    case "onTabTypeChange":
      onTabTypeChange(data.payload);
      break;
    default:
      break;
  }
};

const onSelectNode = (payload: Record<string, unknown>) => {
  const nodeId = payload.data;
  const dataStore = useDataStore();
  const node = dataStore.nodesAll.find((item) => item.nodeId === nodeId);
  if (!node) return;
  highlightPreview(node);
};

const onCnacelSelectNode = () => {
  const previewStore = usePreviewStore();
  previewStore.isMenuVisible = false;
  highlightPreview();
};

const onShowPreviewChainModal = (payload: Record<string, unknown>) => {
  const nodeId = payload.data;
  if (payload.type === "node") {
    const dataStore = useDataStore();
    const node = dataStore.nodesAll.find((item) => item.nodeId === nodeId);
    if (!node) return;
    const { sources } = getChainNodesByNode(node);
    drawPreviewChain(node, sources);
  }
};

const onZoomPreviewChainModal = (payload: Record<string, unknown>) => {
  const trans = payload.data as d3.ZoomTransform;
  d3.select("#chainPreviewGroup").attr(
    "transform",
    `translate(${trans.x},${trans.y}) scale(${trans.k})`
  );
};

const onZoomPreviewSvg = (payload: Record<string, unknown>) => {
  const trans = payload.data as d3.ZoomTransform;

  d3.select("#previewGroup").attr(
    "transform",
    `translate(${trans.x},${trans.y}) scale(${trans.k})`
  );
};

const onMapChange = (payload: Record<string, unknown>) => {
  emitter.emit("on:MapChange", payload.data as string);
};

const onTabTypeChange = (payload: Record<string, unknown>) => {
  emitter.emit("on:TabTypeChange", payload.data as string);
};
