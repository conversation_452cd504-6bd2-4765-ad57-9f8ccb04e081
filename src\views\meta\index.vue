<template>
  <div class="flex flex-1 p-2">
    <div class="w-60 pl-4">
      <MetaTree @edit="editwAddGroupModal"></MetaTree>
    </div>

    <div class="flex flex-1 flex-col ml-2">
      <div class="flex my-2 justify-between">
        <div class="w-60">
          <n-input v-model:value="searchValue" placeholder="请输入图元名称或类型" clearable>
          </n-input>
        </div>
        <div>
          <n-button type="primary" size="small" class="mr-4" @click="editwAddGroupModal()">
            <template #icon>
              <n-icon>
                <AddFilled />
              </n-icon>
            </template>
            分组
          </n-button>
          <n-button type="info" size="small" class="mr-4" @click="showEditMetaModal()">
            <template #icon>
              <n-icon>
                <AddFilled />
              </n-icon>
            </template>
            对象
          </n-button>
          <n-upload
            :show-file-list="false"
            :accept="'.json'"
            @change="handleImportUpload"
            class="inline-block w-auto"
          >
            <n-button type="success" size="small" class="mr-4">
              <template #icon>
                <n-icon>
                  <CloudUpload />
                </n-icon>
              </template>
              导入
            </n-button>
          </n-upload>
          <n-button type="warning" size="small" @click="showExportMetaModal()">
            <template #icon>
              <n-icon>
                <FileExport />
              </n-icon>
            </template>
            导出
          </n-button>
        </div>
      </div>
      <n-data-table
        :columns="columns"
        :data="metaTableData"
        :pagination="pagination"
        :bordered="false"
      />
    </div>
    <EditGroup ref="editGroupModalRef" />
    <EditMeta ref="editMetaModalRef" />
    <MetaIconData ref="metaIconDataRef"></MetaIconData>
    <ExportMeta ref="exportMetaModalRef" />
  </div>
</template>

<script setup lang="ts">
import { computed, h, onMounted, ref } from "vue";
import { NButton, NIcon, NImage, type UploadFileInfo, useDialog } from "naive-ui";

import Path from "@/assets/images/meta/Path.svg?url";
import Text from "@/assets/images/meta/Text.svg?url";
import MetaTree from "@/components/MetaIcon/MetaTree/index.vue";
import EditGroup from "@/components/MetaIcon/Modal/EditGroup.vue";
import EditMeta from "@/components/MetaIcon/Modal/EditMeta.vue";
import MetaIconData from "@/components/MetaIcon/Modal/MetaIconData/index.vue";
import ExportMeta from "@/components/MetaIcon/Modal/ExportMeta.vue";
import { useMetaStore } from "@/stores";
import { useDataBindStore } from "@/stores/";
import type { IGroupModel, IMetaItem } from "@/types";
import {
  AddFilled,
  CloudUpload,
  DataObject,
  Delete,
  FileExport,
  ImageEdit24Regular
} from "@/utils/components/icons";
import { deleteMeta as deleteMetaByHttp } from "@/utils/http/apis";

const dialog = useDialog();

const metaStore = useMetaStore();
const dataBindStore = useDataBindStore();
const editGroupModalRef = ref<InstanceType<typeof EditGroup> | null>(null);
const editMetaModalRef = ref<InstanceType<typeof EditMeta> | null>(null);
const metaIconDataRef = ref<InstanceType<typeof MetaIconData> | null>(null);
const exportMetaModalRef = ref<InstanceType<typeof ExportMeta> | null>(null);

const searchValue = ref("");

const pagination = {
  pageSize: 10
};

const columns = [
  {
    title: "序号",
    key: "index"
  },
  {
    title: "类型",
    key: "objType"
  },
  {
    title: "名称",
    key: "objName"
  },
  {
    title: "对象类型",
    key: "compClass",
    render(row: IMetaItem) {
      return h(
        "span",
        {},
        {
          default: () => {
            if (row.compClass === "link") {
              return "连线";
            } else if (row.compClass === "text") {
              return "文字";
            } else {
              return "节点";
            }
          }
        }
      );
    }
  },
  {
    title: "图标",
    key: "objImg",
    render(row: IMetaItem) {
      return h(NImage, {
        src: row.compClass === "link" ? Path : row.compClass === "text" ? Text : row.objImgUrl,
        width: 30,
        height: 30
      });
    }
  },
  {
    title: "操作",
    key: "actions",
    width: 100,
    render(row: IMetaItem) {
      return [
        h(
          NButton,
          {
            text: true,
            type: "primary",
            title: "编辑",
            style: "font-size: 24px"
          },
          {
            default: () =>
              h(
                NIcon,
                {
                  size: 20,
                  class: "mr-2",
                  onClick: () => {
                    showEditMetaModal(row);
                  }
                },
                { default: () => h(ImageEdit24Regular) }
              )
          }
        ),
        h(
          NButton,
          {
            text: true,
            type: "primary",
            title: "数据",
            style: "font-size: 24px"
          },
          {
            default: () =>
              h(
                NIcon,
                {
                  size: 20,
                  class: "mr-2",
                  onClick: () => {
                    showDataBindModal(row);
                  }
                },
                { default: () => h(DataObject) }
              )
          }
        ),
        h(
          NButton,
          {
            text: true,
            type: "error",
            title: "删除",
            style: "font-size: 24px"
          },
          {
            default: () =>
              h(
                NIcon,
                {
                  size: 20,
                  onClick: () => {
                    deleteMeta(row);
                  }
                },
                { default: () => h(Delete) }
              )
          }
        )
      ];
    }
  }
];

const metaTableData = computed(() => {
  return metaStore.metaTableData.filter((item) => {
    return item.objName.includes(searchValue.value) || item.objType.includes(searchValue.value);
  });
});

const deleteMeta = (row: IMetaItem) => {
  dialog.warning({
    title: "警告",
    content: "确定删除当前数据吗？",
    positiveText: "确定",
    negativeText: "取消",
    maskClosable: false,
    closeOnEsc: false,
    onPositiveClick: async () => {
      await deleteMetaByHttp(row.objType);
      window.$message.success("删除成功");
      metaStore.getMetaData();
    },
    onAfterLeave: () => {}
  });
};

const editwAddGroupModal = (val?: IGroupModel) => {
  editGroupModalRef.value?.show(val);
};
const showEditMetaModal = (val?: IMetaItem) => {
  editMetaModalRef.value?.show(val);
};

const showDataBindModal = (val: IMetaItem) => {
  metaIconDataRef.value?.show(val);
};

const handleImportUpload = (options: { file: UploadFileInfo; fileList: UploadFileInfo[] }) => {
  window.$message.warning("功能暂未实现");
  const file = options.file.file;
  if (!file) {
    window.$message.error("上传文件失败");
    return;
  }
  // uploadFile(formData)
  //   .then((res) => {
  //     // 假设后端返回了处理好的配置文件路径或数据
  //     console.log("上传成功，返回数据:", res);
  //     window.$message.success("导入成功");

  //     // 刷新数据
  //     metaStore.getMetaData();
  //     metaStore.getMetaList();
  //   })
  //   .catch((error) => {
  //     console.error("导入失败:", error);
  //     window.$message.error("导入失败，请检查文件格式");
  //   });
};

const showExportMetaModal = () => {
  exportMetaModalRef.value?.show();
};

onMounted(() => {
  metaStore.getMetaData();
  metaStore.getMetaList();
  dataBindStore.getDataExtractList();
});
</script>

<style></style>
