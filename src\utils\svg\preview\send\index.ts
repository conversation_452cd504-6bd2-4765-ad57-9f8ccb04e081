import router from "@/router";
import type { ILink } from "@/types";
import request from "@/utils/http";

import { CompanyList } from "./company";

const getCompanySummary = (len: number) => {
  return [
    { name: "zghx_company_num", value: len },
    { name: "zghx_project_num", value: 2 * len },
    { name: "zghx_property_pecent", value: len },
    { name: "zghx_contract", value: 5 * len }
  ];
};

const getCompanyIds = (links: ILink[]) => {
  const nameSet = new Set<string>();
  const companyIds: string[] = [];
  links.forEach((link) => {
    const nameStr = link.metaData?.name;
    const names = nameStr?.split("、");
    if (!names) return;
    names.forEach(nameSet.add, nameSet);
  });

  CompanyList.forEach((company) => {
    if (nameSet.has(company.name)) {
      companyIds.push(company.code);
    }
  });

  return companyIds;
};

export const sendMessage = (links?: ILink[]) => {
  const { componentCode } = router.currentRoute.value.query;
  const params: {
    type: string;
    source: string[];
    componentCode: string;
    first: boolean;
  } = {
    type: "",
    source: [],
    componentCode: componentCode as string,
    first: true
  };
  let companyIds: string[] = [];
  if (links) {
    companyIds = getCompanyIds(links);
    params.source = companyIds;
  } else {
    params.type = "all";
  }

  window.top?.postMessage(params, "*");

  const data = getCompanySummary(links ? companyIds.length : CompanyList.length);
  data.forEach((item) => {
    sendData(item);
  });
};

export const sendData = async (param: { name: string; value: number }) => {
  request.post({
    // url: "http://172.19.136.51:6818/dataSource/addRedisCmdDataByKey",
    url: "http://172.19.43.15:6818/dataSource/addRedisCmdDataByKey",
    data: param
  });
};
