<template>
  <g :id="nodeId" :transform="transform" class="node cursor-pointer">
    <Rect v-if="data.nodeType === 'rect'" :data="data" />
    <Ellipse v-else-if="data.nodeType === 'circle'" :data="data" />
    <Text v-else-if="data.nodeType.includes('text')" :data="data" />
    <Image v-else :data="data"> </Image>
  </g>
</template>

<script setup lang="ts">
import { computed, onMounted } from "vue";
import * as d3 from "d3";

import type { INode } from "@/types";
import { bindNodeDrag } from "@/utils/editor/event";

import Image from "./Image.vue";
import Rect from "./Rect.vue";
import Ellipse from "./Ellipse.vue";
import Text from "./Text.vue";

const props = defineProps<{
  data: INode;
}>();

const nodeId = computed(() => {
  return `node_${props.data.nodeId.split("#")[0]}`;
});

const transform = computed(() => {
  return `translate(${props.data.x}, ${props.data.y}) rotate(${props.data.rotate || 0} ${
    props.data.width / 2
  } ${props.data.height / 2} )`;
});

onMounted(() => {
  const node = d3.select<SVGGElement, INode>(`#${nodeId.value}`).data([props.data]);

  bindNodeDrag(node);
});
</script>

<style></style>
