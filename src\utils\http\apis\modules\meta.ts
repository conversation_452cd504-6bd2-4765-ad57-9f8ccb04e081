import type { IGroupModel, IMetaModel, IMetaSource, IScriptItem, IScriptSource } from "@/types";

import request from "../../index";

export const getGroupList = () => {
  return request.get<IMetaSource[]>({ url: "/topoEdit/getGroupList" });
};

export const addGroup = (params: IGroupModel) => {
  return request.post<IMetaSource[]>({
    url: "/topoEdit/insertGroup",
    data: { groupName: params.groupName }
  });
};

export const updateGroup = (params: IGroupModel) => {
  return request.post<IMetaSource[]>({
    url: "/topoEdit/updateGroup",
    data: params
  });
};

export const deleteGroup = (groupId: string) => {
  return request.post<IMetaSource[]>({
    url: "/topoEdit/deleteGroup",
    data: { groupId }
  });
};

export const addMeta = (params: IMetaModel) => {
  params.svgData = "";
  return request.post({
    url: "/topoEdit/insertObj",
    data: params
  });
};

export const updateMeta = (params: IMetaModel) => {
  params.svgData = "";
  return request.post({
    url: "/topoEdit/updateObj",
    data: params
  });
};

export const deleteMeta = (objType: string) => {
  return request.post({
    url: "/topoEdit/deleteObj",
    data: { objType }
  });
};

export const getMapScript = (mapId: string) => {
  return request.get<IScriptSource[]>({
    url: "/topoEdit/getMapScript",
    params: { mapId }
  });
};

export const addScript = (params: IScriptItem) => {
  return request.post({
    url: "/topoEdit/insertMapScript",
    data: params
  });
};

export const deleteScript = (id: number) => {
  return request.post({
    url: "/topoEdit/deleteMapScript",
    data: { id }
  });
};

export const updateScript = (params: IScriptSource) => {
  return request.post({
    url: "/topoEdit/updateMapScript",
    data: params
  });
};
