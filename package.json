{"name": "topo-editor", "version": "0.1.4", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "build:ts": "vite build --mode ts", "release": "release-it && run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "format": "prettier --write src/", "svg": "tsc  --project tsconfig.svg.json "}, "dependencies": {"@codemirror/lang-javascript": "^6.2.4", "@vueuse/components": "^13.3.0", "@vueuse/core": "^13.3.0", "axios": "^1.9.0", "codemirror": "^6.0.1", "d3": "^7.9.0", "d3-dag": "^1.1.0", "js-cookie": "^3.0.5", "mitt": "^3.0.1", "path-intersection": "^3.1.0", "pinia": "^3.0.3", "radash": "^12.1.0", "svg-pathdata": "^7.2.0", "vue": "^3.5.16", "vue-draggable-plus": "^0.6.0", "vue-router": "^4.5.1"}, "devDependencies": {"@release-it/conventional-changelog": "^10.0.1", "@rushstack/eslint-patch": "^1.11.0", "@tsconfig/node22": "^22.0.2", "@types/codemirror": "^5.60.16", "@types/d3": "^7.4.3", "@types/js-cookie": "^3.0.6", "@types/node": "^22.15.31", "@unocss/reset": "^66.1.4", "@unocss/transformer-directives": "^66.1.4", "@vitejs/plugin-vue": "^5.2.4", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.28.0", "eslint-plugin-oxlint": "^1.0.0", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "eslint-plugin-vue": "^10.2.0", "jiti": "^2.4.2", "naive-ui": "^2.41.1", "npm-run-all2": "^8.0.4", "oxlint": "^1.0.0", "prettier": "^3.5.3", "release-it": "^19.0.3", "typescript": "~5.8.3", "unocss": "^66.1.4", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "unplugin-vue-markdown": "^28.3.1", "vite": "npm:rolldown-vite@latest", "vite-plugin-vue-devtools": "^7.7.6", "vite-svg-loader": "^5.1.0", "vue-tsc": "^2.2.10"}}