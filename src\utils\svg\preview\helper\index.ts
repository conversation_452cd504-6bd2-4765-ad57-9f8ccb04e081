import type { ILink, INode } from "@/types";

const linkChains: ILink[] = [];

export const getChainLinksByNode = (node: INode | null | undefined, type?: string) => {
  if (!type) {
    linkChains.length = 0;
  }
  if (node) {
    if (type === "source" || !type) {
      node.sources.forEach((link) => {
        if (linkChains.includes(link)) return;
        linkChains.push(link);
        getChainLinksByNode(link.source, "source");
      });
    }

    if (type === "target" || !type) {
      node.targets.forEach((link) => {
        if (linkChains.includes(link)) return;
        linkChains.push(link);
        getChainLinksByNode(link.target, "target");
      });
    }
  }

  return { linkChains };
};

const chainSourceNodeSet = new Set<INode>();
const chainTargetNodeSet = new Set<INode>();

const getPreviousNodes = (node: INode | null | undefined) => {
  if (node) {
    node.sources.forEach((link) => {
      if (link.source && chainSourceNodeSet.has(link.source)) return;
      link.source && chainSourceNodeSet.add(link.source);
      getPreviousNodes(link.source);
    });
    node.sources.forEach((link) => {
      if (link.source && chainSourceNodeSet.has(link.source)) return;
      link.source && chainSourceNodeSet.add(link.source);
      getPreviousNodes(link.source);
    });
  }

  return Array.from(chainSourceNodeSet);
};

const getNextNodes = (node: INode | null | undefined) => {
  if (node) {
    node.targets.forEach((link) => {
      if (link.target && chainTargetNodeSet.has(link.target)) return;
      link.target && chainTargetNodeSet.add(link.target);
      getNextNodes(link.target);
    });
  }

  return Array.from(chainSourceNodeSet);
};
export const getChainNodesByNode = (node: INode | null | undefined) => {
  chainSourceNodeSet.clear();
  chainTargetNodeSet.clear();

  return {
    sources: getPreviousNodes(node),
    targets: getNextNodes(node)
  };
};
