import * as d3 from "d3";

import { useDataStore, useMapStore, useSvgStore } from "@/stores";
import type { IEnter, ILink, IPosition, ISVGG, IUpdate } from "@/types";
import { BaseLink } from "@/utils/constant";
import { addLink, fomatPath, setLinkRect } from "@/utils/tools";

import { attrLink, attrLinkG, attrSelectedLink } from "../attr";
import { bindLinkDrag } from "../event";

export const appenSelectedLink = (linkG: ISVGG<ILink, null>) => {
  if (!linkG.select<SVGGElement>("path.selected-link").empty()) return;
  const link = linkG.append("path");
  attrSelectedLink(link);
};

export const removeSelectedLink = () => {
  d3.select<SVGGElement, any>("#linkGroup").selectAll("path.selected-link").remove();
};

const appendLink = (enter: IEnter<ILink>) => {
  const enterG = enter.append<SVGGElement>("g");
  const link = enterG.append<SVGPathElement>("path");
  //   const shadowlink = enterG.append<SVGPathElement>("path");
  attrLinkG(enterG);
  attrLink(link);
  bindLinkDrag(enterG);
  setLinkRect(enterG);

  return enterG;
};

const updateLink = (update: IUpdate<ILink>) => {
  const link = update.select<SVGPathElement>("path.link");
  //   const shadowlink = update.select<SVGPathElement>("path.shadow-link");
  attrLink(link);
  setLinkRect(update);
  return update;
};

export const drawLinks = () => {
  const dataStore = useDataStore();
  const linkGroup = d3.select<SVGGElement, any>("#linkGroup");

  linkGroup
    .selectAll<SVGGElement, ILink>("g.link-group")
    .data(dataStore.links, (d: ILink) => d.linkId)
    .join(appendLink, updateLink);
};

const appendLinkSelection = (enter: IEnter<ILink>) => {
  const enterG = enter.append<SVGRectElement>("rect");
  enterG
    .attr("class", "link-selection")
    .attr("fill", "none")
    .attr("stroke-dasharray", "5,5")
    .attr("stroke", "#409eff")
    .attr("stroke-width", 2)
    .attr("pointer-events", "none")
    .attr("x", (d) => d.x - d.linkWidth * 0.5 - 2)
    .attr("y", (d) => d.y - d.linkWidth * 0.5 - 2)
    .attr("width", (d) => d.width + d.linkWidth + 4)
    .attr("height", (d) => d.height + d.linkWidth + 4);
  return enterG;
};

const updateLinkSelection = (update: d3.Selection<SVGRectElement, ILink, SVGGElement, any>) => {
  update.attr("transform", (d) => `translate(${d.transform.x}, ${d.transform.y})`);

  return update;
};

export const drawLinkSelections = () => {
  const dataStore = useDataStore();

  d3.select<SVGGElement, any>("#linkSelectionGroup")
    .selectAll<SVGRectElement, ILink>("rect.link-selection")
    .data(dataStore.linksSelected, (d: ILink) => d.linkId)
    .join(appendLinkSelection, updateLinkSelection, (exit) => exit.remove());
};

const linkPoints: IPosition[] = [];
export const drawLink = (e: PointerEvent) => {
  const svgStore = useSvgStore();
  if (!svgStore.isDrawLink) return;
  console.log("click", e);

  // svgStore.zoomTrans
  const { x, y, k } = svgStore.zoomTrans;
  const { offsetX, offsetY } = e;
  linkPoints.push({
    x: (offsetX - x) / k,
    y: (offsetY - y) / k
  });
  // d3.select<SVGSVGElement, any>("#svgEditor > #map")

  const d = fomatPath(linkPoints, 0);

  d3.select("#drawLinkDraft").attr("d", d);
};

export const drawingLink = (e: PointerEvent) => {
  const svgStore = useSvgStore();
  if (!svgStore.isDrawLink) return;

  const pointsGuide = linkPoints.slice();
  const { offsetX, offsetY } = e;
  const { x, y, k } = svgStore.zoomTrans;
  pointsGuide.push({
    x: (offsetX - x) / k,
    y: (offsetY - y) / k
  });

  const d = fomatPath(pointsGuide, 0);

  d3.select("#drawLinkDraftGuide").attr("d", d);
};

export const clearLinkDraft = () => {
  const svgStore = useSvgStore();
  svgStore.isDrawLink = false;
  linkPoints.length = 0;
  d3.select("#drawLinkDraft").attr("d", "");
  d3.select("#drawLinkDraftGuide").attr("d", "");
};

export const finishDrawLink = async () => {
  const mapStore = useMapStore();

  if (!mapStore.mapInfo || linkPoints.length <= 1) return;
  const d = d3.select("#drawLinkDraftGuide").attr("d");

  const linkStyle = {
    fill: "none",
    stroke: "#63e2b7",
    "stroke-width": 1
  };

  const link = {
    ...BaseLink,
    mapId: mapStore.mapInfo.mapId,
    linkPath: d,
    linkStyles: JSON.stringify(linkStyle),
    metaData: {}
  };
  await addLink(link);
  clearLinkDraft();
};
