// 组件跨屏同步
import { operate } from "./operate";

export let ws: WebSocket;
let isControlClient = false;
let reconnectTimer: number | null = null;
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 5;
const RECONNECT_INTERVAL = 3000; // 3秒重连一次

export function connect() {
  let url = "";

  url = import.meta.env.VITE_WEBSOCKET_URL;

  if (!url) {
    console.error("WebSocket URL is not defined");
    return;
  }

  try {
    ws = new WebSocket(`ws://${url}`);

    ws.onopen = () => {
      console.log("connected");
      // 连接成功后重置重连计数
      reconnectAttempts = 0;
      if (reconnectTimer) {
        clearTimeout(reconnectTimer);
        reconnectTimer = null;
      }
    };

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        if (!isControlClient) {
          operate(data);
        }
        isControlClient = false;
      } catch (error) {
        console.log("🚀 ~ connect ~ error:", error);
      }
    };

    ws.onerror = (error) => {
      console.error("WebSocket connection error:", error);
    };

    ws.onclose = (err) => {
      console.error("disconnected", err);
      attemptReconnect();
    };
  } catch (error) {
    console.error("Failed to establish WebSocket connection:", error);
    attemptReconnect();
  }
}

function attemptReconnect() {
  if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
    console.error(`Failed to reconnect after ${MAX_RECONNECT_ATTEMPTS} attempts`);
    return;
  }

  if (reconnectTimer) {
    clearTimeout(reconnectTimer);
  }

  reconnectTimer = window.setTimeout(() => {
    reconnectAttempts++;
    console.log(`Attempting to reconnect (${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})...`);
    connect();
  }, RECONNECT_INTERVAL);
}

export function disconnect() {
  if (reconnectTimer) {
    clearTimeout(reconnectTimer);
    reconnectTimer = null;
  }

  if (ws && ws.readyState !== WebSocket.CLOSED) {
    ws.close();
  }
}

export function sendSyncMessage(event: string, payload: Record<string, unknown>) {
  isControlClient = true;
  if (!ws) return;
  if (ws.readyState === WebSocket.OPEN) {
    try {
      const data = JSON.stringify({ event, payload });
      ws.send(data);
    } catch (error) {
      console.error("Failed to send message:", error);
    }
  } else {
    console.warn("WebSocket is not connected. Current state:", ws.readyState);
  }
}
