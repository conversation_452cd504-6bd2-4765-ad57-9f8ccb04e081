<template>
  <BaseGrid title="行间距">
    <n-input-number
      style="width: 100%"
      :value="value"
      size="small"
      :show-button="false"
      @update:value="onChange"
    />
  </BaseGrid>
</template>

<script setup lang="ts">
import BaseGrid from "@/components/Common/BaseGrid/index.vue";

const emit = defineEmits<{
  "update:value": [value: string];
}>();

defineProps<{
  value?: string | number | null;
}>();

const onChange = (value: string) => {
  emit("update:value", value);
};
</script>

<style></style>
