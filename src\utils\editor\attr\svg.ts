import * as d3 from "d3";

import { useMapStore, useSvgStore } from "@/stores";
import type { ISVGG, ISVGRect } from "@/types";

export const attrSvg = () => {
  d3.select<SVGSVGElement, any>("#svgEditor").style("background-color", "black");
};

// function checkIntersection(svgPath, rect) {
//   const path = new Path2D(svgPath);
//   const rectPath = new Path2D();
//   rectPath.rect(rect.x, rect.y, rect.width, rect.height);

//   const intersections = d3.intersection(path, rectPath);
//   return intersections.length > 0;
// }

export const attrSvgDrag = (isSpaceDown: boolean) => {
  // 示例
  // const svgPath = "M10 10 H 90 V 90 H 10 L 10 10";
  // const rect = { x: 50, y: 50, width: 40, height: 40 };

  // const isIntersecting = checkIntersection(svgPath, rect);
  // console.log(isIntersecting); // 输出 true 或 false
  d3.select<SVGSVGElement, any>("#svgEditor").style("cursor", isSpaceDown ? "grab" : "auto");
};

export const attrSvgDraging = (isDragging: boolean) => {
  d3.select<SVGSVGElement, any>("#svgEditor").style("cursor", isDragging ? "grabbing" : "grab");
};

const getBgFill = () => {
  const mapStore = useMapStore();
  const svgStore = useSvgStore();
  let fill = svgStore.defaultBgFill;
  if (svgStore.isBgSHow) {
    fill = mapStore.mapInfo?.background ? "url(#mapBg)" : svgStore.defaultBgFill;
  }
  return fill;
};

export const attrMapBackground = () => {
  const mapBackground = d3.select<SVGRectElement, any>("#mapBackground");
  const mapStore = useMapStore();
  const { width, height } = mapStore.mapSize;
  mapBackground.attr("width", width).attr("height", height).attr("fill", getBgFill());
};

export const attrMap = (map: ISVGG<any, HTMLElement>, trans: d3.ZoomTransform) => {
  map
    // .transition()
    // .duration(300)
    .attr("transform", `translate(${trans.x},${trans.y}) scale(${trans.k})`);
};

export const attrSeletView = (
  selectionRect: ISVGRect<any>,
  position: { x: number; y: number },
  size: { width: number; height: number },
  isHide?: boolean
) => {
  selectionRect
    .attr("width", size.width)
    .attr("height", size.height)
    .attr("x", position.x)
    .attr("y", position.y)
    .attr("fill", "transparent")
    .attr("stroke", "white")
    .attr("stroke-width", 1)
    .attr("stroke-dasharray", "5 5")
    .style("display", isHide ? "none" : "block");
};
