<template>
  <div class="fixed left-0 top-0 w-full h-full">
    <div class="fixed left-0 top-0 w-full h-full flex flex-col overflow-hidden">
      <EditorHeader></EditorHeader>
      <div class="relative flex flex-1 editor-layout">
        <n-layout has-sider>
          <LeftPanel />
          <SvgEditor />
        </n-layout>
        <AttributeTrigger />
        <RightPanel />
        <Shortcut />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from "vue";

import SvgEditor from "@/components/SvgEditor/Editor/index.vue";
import EditorHeader from "@/components/SvgEditor/Header/index.vue";
import LeftPanel from "@/components/SvgEditor/Panel/Left/index.vue";
import RightPanel from "@/components/SvgEditor/Panel/Right/index.vue";
import AttributeTrigger from "@/components/SvgEditor/Panel/Right/Trigger/index.vue";
import Shortcut from "@/components/SvgEditor/Shortcut/index.vue";
import { useOperationManager } from "@/hooks/editor/useOperationManager";
import { useSvg } from "@/hooks/svg/useSvg";
import { useDataBindStore } from "@/stores";

useSvg();
useOperationManager();

const dataBindStore = useDataBindStore();

onMounted(() => {
  dataBindStore.getDataExtractList();
});
</script>

<style scoped></style>
