import { useDataStore } from "@/stores";
import type { INode, IPosition } from "@/types";

// 从 dataStore.nodes 中遍历节点，找到最近的节点
export const getAdsorbNode = (position: IPosition, cb: (node: INode | undefined) => void) => {
  const dataStore = useDataStore();
  const { x, y } = position;

  const nodes = dataStore.nodes;
  const node = nodes.find((node) => {
    return (
      x >= node.x - 10 &&
      x <= node.x + node.width + 10 &&
      y >= node.y - 10 &&
      y <= node.y + node.height + 10
    );
  });
  cb(node);
};

/**
 * 获取到最近的节点后
 * 节点的四个边的中心和节点的中心进行比较，找到最近的点
 */
export const getAdsorbPoint = (node: INode, position: IPosition) => {
  const { x, y } = position;
  const { x: nx, y: ny, width, height } = node;

  const points = [
    { x: nx + width / 2, y: ny },
    { x: nx + width, y: ny + height / 2 },
    { x: nx + width / 2, y: ny + height },
    { x: nx, y: ny + height / 2 },
    { x: nx + width / 2, y: ny + height / 2 }
  ];

  let min = Infinity;
  let index = 0;
  points.forEach((point, i) => {
    const distance = Math.sqrt((x - point.x) ** 2 + (y - point.y) ** 2);
    if (distance < min) {
      min = distance;
      index = i;
    }
  });

  return points[index];
};

// 计算向量
function vector(p1: IPosition, p2: IPosition) {
  return { x: p2.x - p1.x, y: p2.y - p1.y };
}

// 计算向量的叉积，判断方向
function crossProduct(v1: IPosition, v2: IPosition) {
  return v1.x * v2.y - v1.y * v2.x;
}

// 计算两个点之间的角度
function calculateAngle(prePoint: IPosition, nextPoint: IPosition) {
  const dx = nextPoint.x - prePoint.x; // x方向的差值
  const dy = nextPoint.y - prePoint.y; // y方向的差值
  const radians = Math.atan2(dy, dx); // 计算弧度
  const degrees = radians * (180 / Math.PI); // 将弧度转换为角度
  return degrees;
}

// 设置path为直角
export function makeRightAngle(prePoint: IPosition, point: IPosition, nextPoint: IPosition) {
  // 计算prePoint -> point 和 point -> nextPoint 的向量
  const v1 = vector(prePoint, point);

  // 计算prePoint -> nextPoint 的向量
  const v3 = vector(prePoint, nextPoint);

  // 计算prePoint -> nextPoint 的夹角
  const angle = calculateAngle(prePoint, nextPoint);
  console.log("🚀 ~ makeRightAngle ~ angle:", angle);

  // 计算叉积，判断point 在prePoint -> nextPoint 的左边还是右边
  const cross = crossProduct(v1, v3);

  // 如果叉积为正，point在左边；如果为负，point在右边
  let isLeft = cross > 0;
  if (angle < 0 || angle > 90) {
    isLeft = !isLeft;
  }
  // 根据直角的方向，调整 `point` 的位置
  if (isLeft) {
    point.x = nextPoint.x;
    point.y = prePoint.y;
  } else {
    point.x = prePoint.x;
    point.y = nextPoint.y;
  }

  return point;
}
