// set code cofig
export const StatusDic = {
  "0100": "系统错误",
  "0108": "ID已被占用",
  "0109": "ID不存在",
  "0000": "操作成功",
  "0106": "名称已被占用",
  "0110": "名称不存在",
  "0105": "需要登录",
  "0103": "用户无权限",
  "0111": "参数格式错误",
  "0120": "场景锁获取失败",
  "0121": "场景锁释放",
  "0102": "用户被禁用",
  "0101": "用户名或密码错误",
  "0107": "用户名已被占用",
  "0113": "版本号已存在",
  "0112": "版本号不存在",
  "0114": "已被占用",
  "0116": "缺少参数",
  "0117": " scene不存在",
  "0118": "浏览器标识重复",
  "0122": "无效命令",
  "0123": "场景名称被占用",
  "0124": " 场景code被占用",
  "0125": "指标名称被占用",
  "0126": "数据源名称被占用",
  "0127": " json格式错误",
  "0129": "模式不存在",
  400: "请求错误",
  401: "未授权，请登录",
  403: "拒绝访问",
  404: "请求地址出错",
  408: "请求超时",
  500: "服务器内部错误",
  501: "服务未实现",
  502: "网关错误",
  503: "服务不可用",
  504: "网关超时",
  505: "HTTP版本不受支持",
  //数据服务
  "0130": "测试失败",
  "0131": "SQL执行错误",
  "0132": "系统变量获取失败",
  "0133": "参数含有非法脚本字符",
  "0134": "上传失败",
  "0135": "指标被使用"
};

// set code cofig
export enum HttpCodeConfig {
  success = 200,
  notFound = 404,
  noPermission = 403
}
