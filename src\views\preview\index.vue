<template>
  <div class="absolute top-0 left-0 w-full flex justify-between p-2 z-10">
    <div class="flex">
      <n-button text class="mr-3" @click="toEditor">
        <template #icon>
          <n-icon><ArrowBack /></n-icon>
        </template>
      </n-button>
      <n-cascader
        class="w-200px flex-shrink"
        v-model:value="menuId"
        placeholder="请选择图层"
        expand-trigger="hover"
        :options="menuStore.menuList"
        check-strategy="child"
        filterable
        @update:value="onMapChange"
      />
    </div>
    <n-select
      style="width: 200px"
      v-model:value="searchValue"
      :options="selectOptions"
      filterable
      @update:value="onSelectChange"
    />
  </div>
  <n-dropdown
    trigger="manual"
    placement="bottom-start"
    :options="options"
    :x="commonStore.mousePosition.x"
    :y="commonStore.mousePosition.y"
    :show="previewStore.isMenuVisible"
    @select="onMenuSelect"
    @clickoutside="() => (mapStore.isMenuVisible = false)"
    @contextmenu="($event: MouseEvent) => $event.preventDefault()"
  >
  </n-dropdown>

  <div id="previewLayout" class="relative h-100vh w-full">
    <svg
      id="previewSvg"
      xmlns="http://www.w3.org/2000/svg"
      @contextmenu="($event: MouseEvent) => $event.preventDefault()"
    >
      <defs>
        <!-- 用作箭头的 marker -->
        <marker
          id="arrow"
          viewBox="0 0 10 10"
          refX="8"
          refY="5"
          markerWidth="6"
          markerHeight="6"
          orient="auto-start-reverse"
        >
          <path fill="#ccc" d="M 0 0 L 10 5 L 0 10 L 2 5 z" />
        </marker>
        <marker
          id="arrowActive"
          viewBox="0 0 10 10"
          refX="8"
          refY="5"
          markerWidth="6"
          markerHeight="6"
          orient="auto-start-reverse"
        >
          <path :fill="ActiveColor" d="M 0 0 L 10 5 L 0 10 L 2 5 z" />
        </marker>
      </defs>
    </svg>
    <svg id="previewChain" class="absolute w-full h-full hidden"></svg>
  </div>
  <div class="absolute right-2 bottom-2 h-12 px-2 flex items-center bg-#181818">
    <n-button text style="font-size: 24px" @click="resetPreviewSvgSizePosition">
      <n-icon>
        <Location />
      </n-icon>
    </n-button>
  </div>
  <div class="absolute left-0 bottom-0" :style="tabPanelStyle">
    <img :src="tabPanelUrl" alt="" class="object-contain w-full h-full" />
    <div class="absolute left-0 top-0 flex justify-center w-full h-26% p-x-5% cursor-pointer">
      <div class="flex-1 h-full" @click="onTabTypeClick('node')"></div>
      <div class="flex-1 h-full" @click="onTabTypeClick('production')"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onBeforeUnmount, onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import type { CascaderOption, DropdownOption, SelectOption, TreeOption } from "naive-ui";

import DefaultBg from "@/assets/images/preview/panel/default.png";
import NodeTypeBg from "@/assets/images/preview/panel/node_type.png";
import ProductionRelationBg from "@/assets/images/preview/panel/production_relation.png";
import { useCommonStore, useDataStore, useMapStore, useMenuStore, usePreviewStore } from "@/stores";
import type { IMapSource, INode } from "@/types";
import { ArrowBack, Location } from "@/utils/components/icons";
import emitter from "@/utils/mitt";
import { ActiveColor } from "@/utils/svg/preview/attr";
import { drawPreview, drawPreviewModal } from "@/utils/svg/preview/draw";
import { positionPreview, resetPreviewSvgSizePosition } from "@/utils/svg/preview/event";
import { sendSyncMessage } from "@/utils/sync";
import { getNodeLinkList } from "@/utils/tools";

const menuStore = useMenuStore();
const mapStore = useMapStore();
const dataStore = useDataStore();
const commonStore = useCommonStore();
const previewStore = usePreviewStore();

const route = useRoute();
const router = useRouter();
const menuId = ref<string>();
const searchValue = ref<string>();

const options = [
  {
    label: "Gis",
    key: "Gis"
  },
  {
    label: "技术特点",
    key: "TechFeature"
  },
  {
    label: "关联企业",
    key: "RelatedEnterprise"
  },
  {
    label: "说明",
    key: "NodeDescription"
  }
];

const mapIdOptions = {
  default: "",
  node: "",
  production: ""
};

const getMap = (mapId: string, menuList?: TreeOption[]) => {
  menuList?.forEach((element) => {
    if (element.isMenu) {
      getMap(mapId, element.children);
    } else {
      if (element.key === mapId) {
        onMapChange(mapId, element);
      }
    }
  });
};

const initEvent = () => {
  emitter.on("on:MapChange", async (value: string) => {
    getMap(value, menuStore.menuList);
  });
  emitter.on("on:TabTypeChange", (value: string) => {
    tabType.value = value as ITabType;
  });
};

onMounted(async () => {
  await menuStore.getMenuList();
  if (route.query.mapIds) {
    const mapIds = (route.query.mapIds as string)?.split(",");
    mapIdOptions.default = mapIds[0];
    mapIdOptions.node = mapIds[1];
    mapIdOptions.production = mapIds[2];
  }

  const mapId = mapIdOptions.default || "TcMJ7IzreI";
  getMap(mapId, menuStore.menuList);
  initEvent();
});

onBeforeUnmount(() => {
  emitter.off("on:MapChange");
  emitter.off("on:TabTypeChange");
});

const onMapChange = async (
  value: string,
  option: CascaderOption,
  pathValues?: Array<CascaderOption | null>
) => {
  if (pathValues) {
    sendSyncMessage("onMapChange", { type: "preview", data: value });
  }
  mapStore.setMapInfo(option.raw as IMapSource);
  await getNodeLinkList(value);
  drawPreview();
};

const selectOptions = computed(() => {
  return dataStore.nodesAll.map((node) => ({
    label: node.nodeText,
    value: node.nodeId,
    raw: node
  }));
});

const onSelectChange = (val: string, options: SelectOption) => {
  positionPreview(options.raw as INode);
};

const onMenuSelect = (key: string, option: DropdownOption) => {
  previewStore.isMenuVisible = false;
  previewStore.menuType = key;
  switch (key) {
    case "Gis":
      router.push("/gis");
      break;
    case "TechFeature":
      drawPreviewModal();
      break;
    case "RelatedEnterprise":
      drawPreviewModal();
      break;
    case "NodeDescription":
      drawPreviewModal();
      break;
  }
};

const toEditor = () => {
  router.push("/editor");
};

const tabPanelStyle = computed(() => {
  // 获取body的宽度和高度
  const height = document.body.clientHeight;
  return {
    height: height * 0.14 + "px",
    marginBottom: height * 0.02 + "px"
  };
});

// 左下角控制面板
type ITabType = "default" | "node" | "production";
const tabType = ref<ITabType>("default");

const tabPanelUrl = computed(() => {
  switch (tabType.value) {
    case "default":
      return DefaultBg;
    case "node":
      return NodeTypeBg;
    case "production":
      return ProductionRelationBg;
    default:
      return DefaultBg;
  }
});

const onTabTypeClick = (type: ITabType) => {
  if (type === tabType.value) {
    tabType.value = "default";
  } else {
    tabType.value = type;
  }

  const mapId = mapIdOptions[tabType.value];
  if (!mapId) return;
  sendSyncMessage("onMapChange", { type: "preview", data: mapId });
  getMap(mapId, menuStore.menuList);
  sendSyncMessage("onTabTypeChange", { type: "preview", data: tabType.value });
};
</script>

<style>
.preview-chain-node-text {
  height: 100%;
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: transparent; /* 将文本颜色设置为透明 */
  /* 定义背景渐变 */
  background: linear-gradient(to bottom, #e4d5b5 0%, #cfb994 50%, #b69a71 100%);
  background-clip: text;
  -webkit-background-clip: text;
}

.float-bubble {
  display: flex;
  padding: 0 10px;
  justify-content: center;
  align-items: center;
  word-break: break-all;
  word-wrap: break-word;
  width: 100%;
  height: 100%;
  font-size: 12px;
  color: #9a9a9a; /* 文字颜色 */
  position: relative;
  background: #414141; /* 气泡背景颜色 */
  border-radius: 3px; /* 圆角 */
  border: 1px solid #6e7e80b0; /* 边框 */
}

.float-bubble::before {
  content: "";
  position: absolute;
  top: 0;
  right: 50%;
  margin-top: -17px;
  border: 8px solid transparent;
  border-bottom-color: #6e7e80b0;
  transform: translateX(8px);
}
.float-bubble::after {
  content: "";
  position: absolute;
  top: 0;
  right: 50%;
  margin-top: -20px;
  border-style: solid;
  border: 8px solid transparent;
  border-bottom-color: #414141;
  transform: translate(8px, 4.5px);
}
.preview-node-text {
  position: absolute;
  left: 0px;
  top: 0px;
  display: flex;
  align-items: center;
  white-space: pre-wrap;
  background-repeat: no-repeat;
}
</style>
