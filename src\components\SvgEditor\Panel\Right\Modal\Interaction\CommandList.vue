<template>
  <n-modal
    v-model:show="isVisible"
    preset="dialog"
    title="命令列表"
    size="huge"
    :bordered="false"
    :show-icon="false"
    :close-on-esc="false"
    :maskClosable="false"
    positive-text="确认"
    negative-text="取消"
    @positive-click="submit"
    @negative-click="hide"
    style="margin-top: 20vh; width: 600px"
  >
    <!-- <n-input-group class="mb-2" size="small">
      <n-input v-model:value="searchValue" :style="{ width: '50%' }" size="small" />
      <n-button type="primary" ghost size="small" @click="getCommandOptions"> 搜索 </n-button>
    </n-input-group>
    <n-transfer
      v-model:value="commandNames"
      :options="commandNameOptions"
      @update:value="onCommandUpdata"
    /> -->

    <n-select
      v-model:value="commandNames"
      multiple
      filterable
      placeholder="搜索命令"
      :options="commandNameOptions"
      :loading="loading"
      clearable
      remote
      :clear-filter-after-select="false"
      @search="getCommandOptions"
      @update:value="onCommandUpdata"
    />
  </n-modal>
</template>

<script setup lang="ts">
import { ref } from "vue";
import type { SelectOption } from "naive-ui";

import type { ICommand } from "@/types";
import { getControlCmdByResourceCode } from "@/utils/http/apis";

const emit = defineEmits(["update:value"]);
const isVisible = ref(false);

const commandNames = ref<string[]>([]);
const commandNameOptions = ref<SelectOption[]>([]);
const selectedCommands = ref<SelectOption[]>([]);
const loading = ref(false);

const getCommandOptions = async (query: string) => {
  if (!query) {
    window.$message.warning("请输入搜索内容");
    return;
  }
  loading.value = true;
  try {
    const list = await getControlCmdByResourceCode(query);
    loading.value = false;
    commandNameOptions.value = list.map((item) => {
      return {
        label: item.cmdName,
        value: item.cmdName,
        action: item.action
      };
    });
  } catch (error) {
    loading.value = false;
  }
};

const onCommandUpdata = (val: string[], option: SelectOption[]) => {
  selectedCommands.value = option;
};

const show = (val: ICommand[]) => {
  commandNames.value = val.map((item) => item.cmdName);
  commandNameOptions.value = val.map((item) => {
    return {
      label: item.cmdName,
      value: item.cmdName,
      action: item.action
    };
  });
  selectedCommands.value = commandNameOptions.value;
  isVisible.value = true;
};

const hide = () => {
  isVisible.value = false;
};

const submit = () => {
  hide();
  const res = selectedCommands.value.map((item) => {
    return {
      cmdName: item.label,
      action: item.action
    };
  });

  emit("update:value", res);
};

defineExpose({
  show
});
</script>
