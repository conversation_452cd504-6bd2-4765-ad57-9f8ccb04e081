import type { IEnter, ILink } from "@/types";

export const appendPreviewLink = (enter: IEnter<ILink>) => {
  const paths = enter
    .append<SVGPathElement>("path")
    .attr("class", "preview-link")
    .attr("id", (d) => `previewLink_${d.linkId}`)
    .attr("d", (d) => d.linkPath)
    .attr("pointer-events", "stroke")
    .attr("marker-end", "url(#arrow)")
    .attr("style", (d) => {
      let style = "";
      for (const key in d.style) {
        style += `${key}:${d.style[key]};`;
      }
      return style;
    });

  return paths;
};
