<template>
  <Teleport to="body">
    <Draggable
      v-if="isVisible"
      class="fixed"
      :initial-value="{ x: innerWidth - 330, y: 40 }"
      :prevent-default="true"
      :handle="handle"
    >
      <n-card class="w-80 h-70vh cursor-move" closable @close="hide" ref="handle">
        <template #header>
          <div class="select-none">模型数据</div>
        </template>
        <n-input-group>
          <n-input size="small" v-model:value="modelName" />
          <n-button size="small" type="primary" ghost> 搜索 </n-button>
        </n-input-group>
      </n-card>
    </Draggable>
  </Teleport>
</template>

<script setup lang="ts">
// import { useDraggable } from "@vueuse/core";
import { UseDraggable as Draggable } from "@vueuse/components";

import { ref, useTemplateRef } from "vue";

const innerWidth = window.innerWidth;

const isVisible = ref(false);
const modelName = ref("");
const handle = useTemplateRef<HTMLInputElement>("handle");

const show = () => {
  isVisible.value = !isVisible.value;
};

const hide = () => {
  isVisible.value = false;
};

defineExpose({
  show,
  hide
});
</script>
