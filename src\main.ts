import { createApp } from "vue";
import { createPinia } from "pinia";

import { deepClone } from "./utils/tools";
import App from "./App.vue";
import router from "./router";

import "virtual:uno.css";
import "@unocss/reset/tailwind-compat.css";
import "@/assets/css/main.css";

// globals.ts
if (!window.structuredClone) {
  window.structuredClone = function structuredClone(objectToClone: any) {
    console.log("🚀 ~ structuredClone ~ objectToClone:", objectToClone);
    return deepClone(objectToClone);
  };
}

const app = createApp(App);
// connect();

app.use(router).use(createPinia()).mount("#app");
