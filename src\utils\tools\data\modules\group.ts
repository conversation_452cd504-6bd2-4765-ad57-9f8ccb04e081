import { useDataStore } from "@/stores";
import type { ILink, INode } from "@/types";

const setGroupNodesLinks = () => {
  const dataStore = useDataStore();
  const { nodesAll, linksAll } = dataStore;

  nodesAll.forEach((node) => {
    dataStore.groups.forEach((group) => {
      if (group.dataIds?.includes(node.nodeId)) {
        if (!node.groupId) {
          node.groupId = [];
        }
        if (!group.nodes) {
          group.nodes = [];
        }

        node.groupId?.push(group.groupId);
        group.nodes.push(node);
      }
    });
  });
  linksAll.forEach((link) => {
    dataStore.groups.forEach((group) => {
      if (group.dataIds?.includes(link.linkId)) {
        if (!link.groupId) {
          link.groupId = [];
        }
        if (!group.links) {
          group.links = [];
        }
        link.groupId?.push(group.groupId);
        group.links.push(link);
      }
    });
  });
};

//0: 绑定类型聚合数据 1: 绑定node或者link分散数据
const setGroupBindData = () => {
  const dataStore = useDataStore();
  const types = new Set<string>();
  dataStore.groups.forEach((group) => {
    if (!group.bindData) {
      group.bindData = [];
    }
    if (group.groupType === 1) {
      group.nodes?.forEach((node) => {
        if (group.bindData?.some((item) => item.nodeLinkId === node.nodeId)) {
          return;
        }
        group.bindData?.push({
          type: node.nodeType,
          detailId: null,
          key: null,
          nodeLinkId: node.nodeId,
          objDataBindInfo: []
        });
      });
      group.links?.forEach((link) => {
        if (group.bindData?.some((item) => item.nodeLinkId === link.linkId)) {
          return;
        }
        group.bindData?.push({
          type: "link",
          detailId: null,
          key: null,
          nodeLinkId: link.linkId,
          objDataBindInfo: []
        });
      });
    } else {
      group.nodes?.forEach((node) => {
        if (group.bindData?.some((item) => item.type === node.nodeType)) {
          return;
        }
        types.add(node.nodeType);
      });
      group.links?.forEach(() => {
        if (group.bindData?.some((item) => item.type === "link")) {
          return;
        }
        types.add("link");
      });

      types.forEach((type) => {
        group.bindData?.push({
          type,
          detailId: null,
          key: null
        });
      });
    }
  });
};
export const initGroupData = () => {
  setGroupNodesLinks();
  setGroupBindData();
};

export const getGroupDataList = (nodes?: INode[], links?: ILink[]) => {
  const list: {
    dataId: string;
    dataType: "node" | "link";
  }[] = [];

  nodes &&
    nodes.forEach((node) => {
      list.push({
        dataId: node.nodeId,
        dataType: "node"
      });
    });

  links &&
    links.forEach((link) => {
      list.push({
        dataId: link.linkId,
        dataType: "link"
      });
    });

  return list;
};
