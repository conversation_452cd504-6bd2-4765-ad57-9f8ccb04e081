# topo-editor

拓扑编辑器是一个基于Vue 3和Vite开发的可视化拓扑图设计工具，支持图元管理、权限控制和画布编辑等功能。

## 项目概述

topo-editor提供了一套完整的拓扑图设计与编辑解决方案，用户可以通过丰富的图元库和直观的操作界面，快速创建和编辑各类拓扑图，适用于网络拓扑、系统架构、流程图等多种场景。该项目使用现代前端技术栈构建，包括Vue 3、TypeScript、Vite等，提供高效的开发体验和良好的用户交互。

## 核心功能

- **可视化编辑**：直观的拖拽式界面，所见即所得
- **图元管理**：丰富的图元库，支持自定义图元
- **画布组合**：支持图元组合与分组管理
- **权限控制**：细粒度的权限管理机制
- **实时预览**：编辑过程中实时预览效果
- **导入导出**：支持多种格式的导入导出
- **连线工具**：创建和编辑不同类型的连接线
- **撤销/重做**：支持多步操作的撤销和重做
- **自动布局**：支持多种布局算法自动排列图元
- **地图集成**：支持基于Leaflet的地理信息展示

## 模块介绍

### 编辑工具 (Editor)

编辑工具模块是整个拓扑编辑器的核心功能区域，提供了丰富的编辑功能：

- 画布操作：缩放、平移、对齐等基本操作
- 图元操作：添加、删除、编辑图元属性
- 连线工具：创建和编辑不同类型的连接线
- 布局管理：自动和手动布局功能
- 撤销/重做：支持多步操作的撤销和重做

### 图元管理 (MetaIcon)

图元管理模块负责维护和管理系统中的各类图元：

- 图元分类：系统内置图元和自定义图元的分类管理
- 图元上传：支持用户上传自定义图元
- 图元编辑：图元属性和样式的编辑功能
- 图元预览：快速预览图元效果
- 图元搜索：按名称和分类搜索图元

### 权限管理 (Permission)

权限管理模块提供了细粒度的权限控制机制：

- 用户角色：不同角色拥有不同的操作权限
- 资源权限：控制对图元、模板的访问权限
- 操作权限：控制编辑、导出等功能的使用权限
- 权限配置：灵活配置各类权限规则

### 系统管理 (System)

系统管理模块提供了整个应用的配置和管理功能：

- 用户管理：用户账号的增删改查
- 系统设置：全局参数配置
- 日志审计：操作日志的记录和查询
- 数据备份：工作成果的备份与恢复
- 性能监控：系统资源使用情况监控

### 画布组合 (Combine)

画布组合模块提供了图元组合和分组管理功能：

- 图元组合：将多个图元组合为一个整体
- 分组管理：对图元进行分组，便于批量操作
- 层级控制：调整图元的层级关系
- 模板保存：将常用组合保存为模板，方便复用

### 图表引擎 (Engine)

图表引擎是拓扑编辑器的底层核心，负责渲染和计算：

- 图形渲染：基于D3和SVG的高性能渲染
- 布局计算：各种拓扑布局算法的实现
- 数据绑定：元素与数据模型的双向绑定
- 交互处理：处理用户的各类交互操作
- 事件系统：完善的事件触发与响应机制

## 技术栈

- **前端框架**：Vue 3 + TypeScript
- **构建工具**：Vite
- **UI组件库**：Naive UI
- **状态管理**：Pinia
- **路由管理**：Vue Router
- **开发语言**：TypeScript
- **包管理工具**：pnpm

## 开发环境配置

### 环境要求

- Node.js >= 16.0.0
- pnpm >= 7.0.0

### 安装依赖

```bash
# 全局安装pnpm（如果尚未安装）
npm install -g pnpm

# 安装项目依赖
pnpm install
```

### 开发服务器启动

```bash
pnpm dev
```

开发服务器默认运行在 http://localhost:5173

### 构建项目

```bash
pnpm build
```

### 代码检查

```bash
pnpm lint
```

### 推荐开发工具

- Visual Studio Code
  - Vue Language Features (Volar) 扩展
  - TypeScript Vue Plugin (Volar) 扩展
  - ESLint 扩展
  - Prettier 扩展

### 项目配置文件

- `vite.config.ts`: Vite构建工具配置
- `tsconfig.json`: TypeScript配置
- `.eslintrc.js`: ESLint代码规范配置
- `.prettierrc`: 代码格式化配置
- `pnpm-lock.yaml`: pnpm依赖锁定文件
