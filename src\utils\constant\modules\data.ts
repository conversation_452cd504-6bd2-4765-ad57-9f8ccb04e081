import type { IOriginalLink, IOriginalNode } from "@/types";

export const BaseNode: IOriginalNode = {
  mapId: "",
  domId: "",
  nodeType: "",
  compClass: "",
  nodePosition: "",
  nodeSize: "",
  nodeStyles: `{"fill": "#19be6b","background-size": "auto"}`,
  metaData: {},
  rotate: 0, //旋转角度
  nodeText: "", //节点文字
  fontSize: "14", //节点字号
  fontColor: "#101014", //节点字色
  textPosition: "", //文字位置
  textStyles: "", //文字样式
  bindData: {}, //关联数据
  bindMap: {}, //关联图层
  sublayerList: []
};

export const BaseLink: IOriginalLink = {
  mapId: "",
  domId: "",
  linkType: "",
  linkStyles: "",
  metaData: {},
  script: "",
  dashedLink: "",
  compClass: "",
  linkPath: "",
  linkWidth: 1,
  linkAnimations: {},
  fromObj: "",
  endObj: "",
  bindData: {},
  bindMap: {},
  sublayerList: []
};
