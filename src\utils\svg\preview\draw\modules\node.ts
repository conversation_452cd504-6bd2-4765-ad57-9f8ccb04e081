import * as d3 from "d3";

import type { IEnter, INode } from "@/types";
import { getNodeImage } from "@/utils/tools";

import { bindPreviewNodeEvent } from "../../event";

const strokeWidth = 0;
export const appendPreviewNode = (enter: IEnter<INode>) => {
  const enterG = enter
    .append<SVGGElement>("g")
    .attr("class", "preview-node-group")
    .attr("id", (d) => `previewNode_${d.nodeId}`)
    .attr("transform", (d) => `translate(${d.x + strokeWidth / 2}, ${d.y + strokeWidth / 2})`)
    .style("cursor", "pointer");

  // enterG
  //   .append("rect")
  //   .attr("class", "preview-node-rect")
  //   .attr("width", (d) => d.width)
  //   .attr("height", (d) => d.height)
  //   .attr("style", (d) => {
  //     let style = "";
  //     for (const key in d.style) {
  //       style += `${key}:${d.style[key]};`;
  //     }
  //     return style;
  //   });

  // enterG
  //   .append("text")
  //   .attr("x", 5)
  //   .attr("y", 3)
  //   .attr("alignment-baseline", "before-edge")
  //   .style("font-size", (d) => d.fontSize)
  //   .style("fill", "#f9f8f6")
  //   .text((d) => d.nodeText);

  enterG
    .append("foreignObject")
    .attr("width", (d) => d.width)
    .attr("height", (d) => d.height)
    .append("xhtml:div")
    .attr("class", "preview-node-text")
    .attr("style", (d) => {
      const style = {
        ...d.style,
        width: `${d.width}px`,
        height: `${d.height}px`,
        "background-color": d.style["fill"] || "transparent",
        "background-image": `url(${getNodeImage(d)})`,
        "background-size": d.style["background-size"] || "unset",
        "background-position": d.style["background-position"] || "unset"
      };

      let styleStr = "";
      for (const key in style) {
        const value = style[key as keyof typeof style];
        styleStr += `${key}:${value};`;
      }
      return styleStr;
    });

  enterG
    .append("foreignObject")
    .attr("width", (d) => d.width)
    .attr("height", (d) => d.height)
    .attr("transform", (d) => `translate(${d.textStyle.x || 0}, ${d.textStyle.y || 0})`)
    .append("xhtml:div")
    //  `translate(${textStyle.x || 0}, ${textStyle.y || 0})`
    .attr("style", (d) => {
      const style = {
        ...d.textStyle,
        display: d.style.display || "block",
        "justify-content": d.style["justify-content"] || "unset",
        "align-items": d.style["align-items"] || "unset",
        width: `${d.width}px`,
        height: `${d.height}px`,
        "font-size": `${d.fontSize}px`,
        color: d.fontColor || "unset"
      };

      let styleStr = "";
      for (const key in style) {
        const value = style[key as keyof typeof style];
        styleStr += `${key}:${value};`;
      }
      return styleStr;
    })
    .text((d) => d.nodeText);

  bindPreviewNodeEvent(enterG);

  return enterG;
};

export const updatePreviewNode = (update: d3.Selection<SVGGElement, INode, SVGGElement, any>) => {
  update.attr("transform", (d) => `translate(${d.x}, ${d.y})`);
  return update;
};
