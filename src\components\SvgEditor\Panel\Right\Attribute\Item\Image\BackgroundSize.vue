<template>
  <BaseGrid title="背景大小">
    <n-select
      :value="value"
      filterable
      placeholder="选择大小"
      size="small"
      :options="BackgroundSizeOptions"
      @update:value="onChange"
    />
  </BaseGrid>
</template>

<script setup lang="ts">
import BaseGrid from "@/components/Common/BaseGrid/index.vue";

defineProps<{
  value?: string | number | null;
}>();

const emit = defineEmits<{
  "update:value": [value: string];
}>();

const BackgroundSizeOptions = [
  {
    label: "auto",
    value: "auto"
  },
  {
    label: "cover",
    value: "cover"
  },
  {
    label: "contain",
    value: "contain"
  },
  {
    label: "100% 100%",
    value: "100% 100%"
  }
];

const onChange = (value: string) => {
  emit("update:value", value);
};
</script>

<style scoped></style>
