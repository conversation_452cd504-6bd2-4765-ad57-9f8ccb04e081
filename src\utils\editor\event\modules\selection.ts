import * as d3 from "d3";
import { SVGPathData } from "svg-pathdata";
import type { CommandM } from "svg-pathdata/dist/types";

import { useCommonStore, useDataStore, useMapStore, useSvgStore } from "@/stores";
import type { ILink } from "@/types";
import { operationManager } from "@/utils/editor/data/operationManager";
import { updateNodesLinks } from "@/utils/http/apis";
import { getBindLinks } from "@/utils/tools";

// import { attrLinkGTrans, attrNodeGTrans } from "../attr";

const startPoint = {
  x: 0,
  y: 0
};

let tx = 0;
let ty = 0;

const linkUpdatedSet = new Set<ILink>();

const dragStart = (e: any) => {
  const commonStore = useCommonStore();
  const svgStore = useSvgStore();
  const dataStore = useDataStore();
  if (!svgStore.editable || commonStore.isSpaceDown) return;
  startPoint.x = e.x;
  startPoint.y = e.y;
  operationManager.preSelect({
    nodes: Array.from(new Set(dataStore.nodesSelected)),
    links: Array.from(
      new Set([...dataStore.linksSelected, ...getBindLinks(dataStore.nodesSelected)])
    )
  });
};

const dragging = (e: any) => {
  const commonStore = useCommonStore();
  const svgStore = useSvgStore();

  if (!svgStore.editable || commonStore.isSpaceDown) return;

  tx = e.x - startPoint.x;
  ty = e.y - startPoint.y;
  if (tx === 0 && ty === 0) return;

  const dataStore = useDataStore();
  dataStore.nodesSelected.forEach((node) => {
    node.x += e.dx;
    node.y += e.dy;
    node.targets.forEach((link) => {
      if (dataStore.linksSelected.includes(link)) return;

      link.linkPath = new SVGPathData(link.linkPath).translate(e.dx, e.dy).toAbs().encode();

      const lastPoint = {
        ...link.points[link.points.length - 1]
      };
      link.points = new SVGPathData(link.linkPath).toAbs().commands;
      link.points[link.points.length - 1] = lastPoint;

      linkUpdatedSet.add(link);
    });
    node.sources.forEach((link) => {
      if (dataStore.linksSelected.includes(link)) return;
      const point0 = link.points[link.points.length - 1] as CommandM;
      link.points[link.points.length - 1] = { ...point0, x: point0.x + e.dx, y: point0.y + e.dy };
      linkUpdatedSet.add(link);
    });
  });

  dataStore.linksSelected.forEach((link) => {
    link.transform.x = tx;
    link.transform.y = ty;
  });
};

const dragEnd = async () => {
  if (tx === 0 && ty === 0) return;
  // const commonStore = useCommonStore();
  // const svgStore = useSvgStore();
  // if (!svgStore.editable || commonStore.isSpaceDown) return;

  const dataStore = useDataStore();
  dataStore.linksSelected.forEach((link) => {
    const d1 = new SVGPathData(link.linkPath);
    const { x, y } = link.transform;

    link.linkPath = d1.translate(x, y).toAbs().encode();
    link.points = new SVGPathData(link.linkPath).commands;
    link.x += x;
    link.y += y;

    link.transform.x = 0;
    link.transform.y = 0;

    // attrLinkGTrans(document.querySelector(`#link_${link.linkId}`) as SVGGElement, 0, 0);
  });

  // operationManager.execute(
  //   {
  //     nodes: Array.from(new Set(dataStore.nodesSelected)),
  //     links: Array.from(new Set([...dataStore.linksSelected, ...linkUpdatedSet]))
  //   },
  //   "UPDATE"
  // );

  //   drawNodesLinks();
  updateNodesLinks({
    nodes: Array.from(new Set(dataStore.nodesSelected)),
    links: Array.from(new Set([...dataStore.linksSelected, ...linkUpdatedSet]))
  });
  linkUpdatedSet.clear();

  tx = 0;
  ty = 0;
};

export const bindDragSelectionEvent = () => {
  const mapStore = useMapStore();

  const drag = d3
    .drag<SVGGElement, any>()
    .on("start", dragStart)
    .on("drag", dragging)
    .on("end", dragEnd);

  d3.select<SVGGElement, any>("#selectionBox").call(drag);

  d3.select<SVGGElement, any>("#selectionBox")

    .on("contextmenu", (e, d) => {
      e.preventDefault();
      e.stopPropagation();
      mapStore.showMapMenu({ x: e.clientX, y: e.clientY }, "group");
    });
};
