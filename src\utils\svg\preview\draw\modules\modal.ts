import * as d3 from "d3";

import chemistryBg from "@/assets/images/bg/chemistry.jpg";
import { usePreviewStore } from "@/stores";
import type { INode, ISVGG } from "@/types";

let previewModal: ISVGG<INode, HTMLElement>;
const width = 500;
const height = 450;

const text = `化学工业是生产化学产品的工业。是一个多行业、多品种，为国民经济各部门和人民生活各方面服务的工业，是重工业的支柱。一般可分为无机化学工业、基本有机化学工业、高分子化学工业和精细化化学工业。`;
const drawPreviewModalClose = () => {
  //   添加关闭按钮
  const closeGroup = previewModal
    .append("g")
    .attr("cursor", "pointer")
    .attr("transform", `translate(${width - 18}, 18)`);

  closeGroup.append("circle").attr("r", 10).attr("cx", 5).attr("cy", -5).attr("fill", "#cccccc");
  closeGroup.append("text").attr("fill", "#fff").text("X");
  closeGroup.on("click", function () {
    previewModal.remove();
  });
};

const drawPreviewModalContent = () => {
  previewModal
    .append("rect")
    .attr("width", width)
    .attr("height", height)
    .attr("fill", "#303030")
    // 阻止鼠标滚轮事件冒泡
    .on("wheel", function (e) {
      e.preventDefault();
      e.stopPropagation();
    });

  previewModal
    .append("text")
    .text((d) => d.nodeText)
    .attr("fill", "#fff")
    .attr("x", 5)
    .attr("y", 20)
    .style("font-size", "18px");

  const content = previewModal
    .append<SVGForeignObjectElement>("foreignObject")
    .attr("x", 10)
    .attr("y", 40)
    .attr("width", width - 20)
    .attr("height", height - 40)
    .append<d3.BaseType>("xhtml:div")
    .style("width", "100%")
    .style("height", "100%")
    .style("overflow", "auto")
    .style("display", "flex")
    .style("flex-direction", "column");

  content.append("xhtml:div").text(text).style("color", "#fff");
  //图片显示
  content
    .append("xhtml:img")
    .attr("src", chemistryBg)
    .style("margin-top", "10px")
    .style("width", "100%");
};

export const drawPreviewModal = () => {
  const previewStore = usePreviewStore();
  if (!previewStore.node) return;

  const x = previewStore.node.x + previewStore.node.width + 5;
  const y = previewStore.node.y - height / 3;

  d3.select<SVGSVGElement, HTMLElement>("#previewModal").remove();

  previewModal = d3
    .select<SVGSVGElement, HTMLElement>("#previewGroup")
    .append<SVGGElement>("g")
    .datum(previewStore.node)
    .attr("id", "previewModal")
    .attr("transform", `translate(${x}, ${y})`);

  drawPreviewModalContent();
  drawPreviewModalClose();
};
