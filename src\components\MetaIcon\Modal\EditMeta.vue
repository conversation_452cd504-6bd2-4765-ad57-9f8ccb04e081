<template>
  <n-modal
    v-model:show="isVisible"
    preset="dialog"
    :title="title"
    size="huge"
    :bordered="false"
    :show-icon="false"
    :close-on-esc="false"
    :maskClosable="false"
    positive-text="确认"
    negative-text="取消"
    @positive-click="submit"
    @negative-click="hide"
    style="margin-top: 20vh; width: 500px"
  >
    <n-form
      ref="groupFormRef"
      :model="groupModel"
      :rules="groupRules"
      label-placement="left"
      label-width="80"
      require-mark-placement="right-hanging"
    >
      <span class="text-xs text-yellow-400 ml-20">弹窗资源编码格式为：{xx}-popup</span>

      <n-form-item label="类型编码" path="objType" ref="objTypeRef">
        <n-input v-model:value="groupModel.objType" placeholder="类型编码" :disabled="isEdit" />
      </n-form-item>
      <n-form-item label="对象名称" path="objName">
        <n-input v-model:value="groupModel.objName" placeholder="对象名称" />
      </n-form-item>
      <n-form-item label="所属分组" path="groupId">
        <n-select
          v-model:value="groupModel.groupId"
          placeholder="所属分组"
          :options="metaStore.groupSelectOptions"
        />
      </n-form-item>
      <n-form-item label="对象类型" path="compClass">
        <n-select
          v-model:value="groupModel.compClass"
          placeholder="对象类型"
          :options="compClassOptions"
          @update:value="onCompClassChange"
        />
      </n-form-item>
      <n-form-item label="脚本" path="script">
        <n-button @click="setScript">设置</n-button>
      </n-form-item>
      <n-form-item label="对象图标" path="objImg" v-if="groupModel.compClass === 'node'">
        <n-upload
          accept="image/*"
          list-type="image-card"
          :max="1"
          :default-file-list="previewFileList"
          :custom-request="customRequest"
          @remove="onImageRemove"
        >
          <n-button>选择文件</n-button>
        </n-upload>
      </n-form-item>
      <!-- <n-form-item label="图标比例" path="groupName">
        <n-select v-model:value="groupModel.selectValue" placeholder="图标比例" :options="[]" />
      </n-form-item> -->
    </n-form>
  </n-modal>
  <CodeMirrorModal
    ref="codeMirrorModalRef"
    @onValueUpdate="onCodeMirrorValueUpdate"
  ></CodeMirrorModal>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import type {
  FormInst,
  FormItemInst,
  FormItemRule,
  NInput,
  UploadCustomRequestOptions
} from "naive-ui";

import CodeMirrorModal from "@/components/SvgEditor/Panel/Left/CodeMirror/index.vue";
import { useMetaStore } from "@/stores";
import type { IMetaItem, IMetaModel } from "@/types";
import { addMeta, updateMeta, uploadFile } from "@/utils/http/apis";

const compClassOptions = [
  { label: "节点", value: "node" },
  { label: "连线", value: "link" },
  { label: "文字", value: "text" }
];

const metaStore = useMetaStore();
const groupFormRef = ref<FormInst | null>(null);
const objTypeRef = ref<FormItemInst | null>(null);
const codeMirrorModalRef = ref<InstanceType<typeof CodeMirrorModal> | null>(null);
const previewFileList = ref<
  {
    id?: string;
    name: string;
    url: string;
    status: string;
  }[]
>([]);
const groupModel = ref<IMetaModel>({
  objType: "",
  objName: "",
  groupId: "",
  compClass: "node",
  script: "",
  objImg: "",
  imgScale: "1",
  svgData: ""
});

const isVisible = ref(false);
const isEdit = ref(false);
const groupRules = {
  objType: [
    {
      validator: (rule: FormItemRule, value: string) => {
        if (!value) {
          return new Error("请输入类型编码");
        } else if (groupModel.value.compClass === "text" && !value.includes("text")) {
          return new Error("对象类型为文本编码格式为：{xx}-text");
        }
        return true;
      },
      trigger: ["blur", "comp-class"]
    }
  ],
  objName: [{ required: true, message: "请输入对象名称", trigger: "blur" }],
  groupId: [{ required: true, message: "请选择所属分组", trigger: "change" }],
  compClass: [{ required: true, message: "请选择对象类型", trigger: "change" }]
};

const title = computed(() => (isEdit.value ? "编辑对象" : "新增对象"));

const customRequest = ({ file, onFinish, onError }: UploadCustomRequestOptions) => {
  const formData = new FormData();
  formData.append("file", file.file as File);

  if (!file.file) return;
  if (file.type === "image/svg+xml") {
    const reader = new FileReader();
    reader.onload = function (e) {
      if (e.target) {
        // groupModel.value.svgData = e.target.result as string; // 这里是SVG文件的内容
      }
    };

    reader.readAsText(new Blob([file.file])); // 以文本格式读取文件
  }

  uploadFile(formData)
    .then((res) => {
      groupModel.value.objImg = res;
      onFinish();
    })
    .catch(() => {
      groupModel.value.objImg = "";
      window.$message.error("上传失败");
      onError();
    })
    .finally(() => {
      groupFormRef.value?.validate(
        () => {},
        (rule) => {
          return rule?.key === "objImg";
        }
      );
    });
};

const onImageRemove = () => {
  groupModel.value.objImg = "";
  groupModel.value.svgData = "";
};

const setScript = () => {
  codeMirrorModalRef.value?.show(groupModel.value.script);
};

const onCodeMirrorValueUpdate = (content: string) => {
  groupModel.value.script = content;
};

const onCompClassChange = () => {
  console.log("objTypeRef.value", objTypeRef.value);

  objTypeRef.value?.validate({ trigger: "comp-class" });
};

const show = (val?: IMetaItem) => {
  isVisible.value = true;
  isEdit.value = !!val;
  if (val) {
    const {
      objType,
      objName,
      groupId = "",
      compClass = "",
      script = "",
      objImg,
      objImgUrl = "",
      imgScale = "",
      svgData = ""
    } = val;

    groupModel.value = {
      objType,
      objName,
      groupId,
      compClass: compClass || "node",
      script,
      objImg,
      imgScale,
      svgData
    };

    previewFileList.value = [
      {
        name: objName,
        url: objImgUrl,
        status: "finished"
      }
    ];
  } else {
    groupModel.value.compClass = "node";
  }
};

const hide = () => {
  isVisible.value = false;
  groupModel.value = {
    objType: "",
    objName: "",
    groupId: "",
    compClass: "",
    script: "",
    objImg: "",
    imgScale: "1",
    svgData: ""
  };
  previewFileList.value = [];
};

const finish = () => {
  const msg = isEdit.value ? "更新成功" : "创建成功";
  hide();
  metaStore.getMetaData();
  window.$message.success(msg);
};

const submit = () => {
  groupFormRef.value?.validate((errors) => {
    if (errors) return;
    const fn = isEdit.value ? updateMeta : addMeta;
    fn(groupModel.value).then(() => {
      finish();
    });
  });

  return false;
};

defineExpose({
  show
});
</script>
