<template>
  <div
    class="fixed right-2 bottom-2 h-12 px-2 flex items-center bg-#181818"
    v-if="mapStore.mapInfo"
  >
    <div class="w-16 text-lg text-center text-#d5d5d5">{{ scale }}</div>
    <div class="mx-4">|</div>
    <div class="flex-1 flex">
      <n-button text class="mr-3" style="font-size: 24px" :color="color" @click="changeEditable">
        <n-icon>
          <Edit />
        </n-icon>
      </n-button>
      <!-- <n-button
        text
        class="mr-3"
        style="font-size: 24px"
        :color="color"
        @click="changeLinkDrawEditable"
      >
        <n-icon>
          <BranchForkLink20RegularIcon />
        </n-icon>
      </n-button> -->
      <n-button text style="font-size: 24px" @click="resetSvgSizePosition">
        <n-icon>
          <Location />
        </n-icon>
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useNotification } from "naive-ui";

import { useMapStore, useSvgStore } from "@/stores";
import { Edit, Location } from "@/utils/components/icons";
import { resetSvgSizePosition } from "@/utils/editor/event";
const svgStore = useSvgStore();
const mapStore = useMapStore();

const notification = useNotification();

const scale = computed(() => {
  return (svgStore.scale * 100).toFixed(1) + "%";
});

const color = computed(() => {
  return svgStore.editable ? "#63e2b7" : "#f5f5f5";
});

const changeEditable = () => {
  svgStore.editable = !svgStore.editable;
  const mode = svgStore.editable ? "编辑" : "预览";
  notification.info({
    content: "模式切换",
    meta: `当前处于${mode}模式`,
    duration: 3000,
    keepAliveOnHover: true
  });
};

const changeLinkDrawEditable = () => {
  svgStore.linkDrawEditable = !svgStore.linkDrawEditable;
  svgStore.editable = false;

  if (svgStore.linkDrawEditable) {
    notification.info({
      content: "模式切换",
      meta: `当前处于编辑连线模式`,
      duration: 3000,
      keepAliveOnHover: true
    });
  }
};
</script>

<style scoped></style>
