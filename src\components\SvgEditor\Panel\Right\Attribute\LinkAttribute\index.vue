<template>
  <BaseGrid title="ID">
    <n-performant-ellipsis>
      {{ dataStore.currentLink?.linkId }}
    </n-performant-ellipsis>
  </BaseGrid>
  <n-divider />
  <n-collapse :default-expanded-names="['1', '2', '3']">
    <n-collapse-item title="连线点" name="1">
      <!-- <BaseGrid title="路径">
        <p>
          {{ dataStore.currentLink?.linkPath }}
        </p>
      </BaseGrid> -->
      <LinkPoints @update:link-points="updateLinkAttribute" />
    </n-collapse-item>
    <n-collapse-item title="样式" name="2">
      <BaseGrid title="线宽">
        <n-input-number
          :default-value="dataStore.currentLink?.linkWidth"
          size="small"
          :min="0"
          placeholder="请输入宽度"
          @update:value="updateLinkWidth"
        >
        </n-input-number>
      </BaseGrid>
      <BaseGrid title="线型">
        <n-select
          :default-value="dataStore.currentLink?.dashedLink"
          :options="LinkTypeOptions"
          placeholder="请选择线型"
          @update:value="updateDashedTypeLink"
        />
      </BaseGrid>
      <StrokeDasharray
        v-if="dataStore.currentLink?.dashedLink === 'dashed'"
        :data="dataStore.currentLink?.style['stroke-dasharray']"
        @update:value="updateDashedLink"
      ></StrokeDasharray>
      <BaseGrid title="颜色">
        <n-color-picker
          :default-value="getRgb(dataStore.currentLink?.style.stroke)"
          :show-preview="true"
          :modes="['hex', 'rgb']"
          size="small"
          @update:value="updateLinkStyle('stroke', $event)"
          @complete="updateLinkAttribute"
        />
      </BaseGrid>
      <FillColor
        :data="getRgb(dataStore.currentLink?.style.fill)"
        @update:value="updateLinkStyle"
        @complete="updateLinkAttribute"
      ></FillColor>
    </n-collapse-item>
    <n-collapse-item title="绑定" name="2">
      <MetaData
        :data="dataStore.currentLink?.metaData || {}"
        @update:metaData="updateMetaData"
      ></MetaData>
      <BindMeta
        :type="dataStore.currentLink?.linkType"
        @update:bindMeta="changeMetaIcon"
      ></BindMeta>
      <BaseGrid title="开始">
        <n-performant-ellipsis class="w-40">
          {{ dataStore.currentLink?.fromObj || "--" }}
        </n-performant-ellipsis>
        <n-button text type="error" class="mx-2">
          <n-icon>
            <Delete />
          </n-icon>
        </n-button>
      </BaseGrid>
      <BaseGrid title="结束">
        <n-performant-ellipsis class="w-40">
          {{ dataStore.currentLink?.endObj || "--" }}
        </n-performant-ellipsis>

        <n-button text type="error" class="mx-2">
          <n-icon>
            <Delete />
          </n-icon>
        </n-button>
      </BaseGrid>
    </n-collapse-item>
    <!-- <n-collapse-item title="自定义样式" name="3">
      <BaseGrid
        v-for="(value, key) in dataStore.currentLink?.style"
        :key="key"
        :title="StyleNameMap[key] ? StyleNameMap[key].title : '12'"
      >
        <n-color-picker
          v-if="StyleNameMap[key].type === 'color'"
          :default-value="getRgb(value + '')"
          :show-preview="true"
          :modes="['hex', 'rgb']"
          size="small"
          @update:value="updateLinkColorAttribute(key, $event)"
          @complete="updateLinkStyle(key, $event)"
        />
        <n-input-number
          v-else-if="StyleNameMap[key].type === 'number'"
          :default-value="parseFloat(value + '')"
          size="small"
          :min="getRange(key)[0]"
          :max="getRange(key)[1]"
          placeholder="请输入"
          @update:value="updateLinkStyle(key, $event)"
        >
          <template #suffix v-if="StyleNameMap[key].suffix">
            {{ StyleNameMap[key].suffix }}
          </template>
        </n-input-number>
      </BaseGrid>
    </n-collapse-item> -->
  </n-collapse>
</template>

<script setup lang="ts">
import BaseGrid from "@/components/Common/BaseGrid/index.vue";

import { useDataStore } from "@/stores";
import { Delete } from "@/utils/components/icons";
import { LinkTypeOptions } from "@/utils/constant";
import { attrUpdateLink } from "@/utils/editor/attr";
import { updateLink } from "@/utils/http/apis";
import { getRgb } from "@/utils/tools";

import BindMeta from "../Item/BindMeta.vue";
import FillColor from "../Item/Common/FillColor.vue";
import StrokeDasharray from "../Item/Link/StrokeDasharray.vue";
import MetaData from "../Item/MetaData.vue";

import LinkPoints from "./Items/LinkPoints.vue";

const dataStore = useDataStore();

// 颜色选择器
const updateLinkColorAttribute = (key: string, value: string) => {
  if (!dataStore.currentLink) return;
  dataStore.currentLink.style[key] = value;
  attrUpdateLink(dataStore.currentLink);
};

// 更新线宽
const updateLinkWidth = (value: number) => {
  if (!dataStore.currentLink) return;
  dataStore.currentLink.linkWidth = value;
  dataStore.currentLink.style["stroke-width"] = value;

  updateLinkAttribute();
};

// 更新线型
const updateDashedTypeLink = (value: string) => {
  if (!dataStore.currentLink) return;
  dataStore.currentLink.dashedLink = value;
  dataStore.currentLink.style["stroke-dasharray"] = value === "solid" ? "none" : "10,5";

  updateLinkAttribute();
};

// 更新虚线
const updateDashedLink = (value: string) => {
  if (!dataStore.currentLink) return;
  dataStore.currentLink.style["stroke-dasharray"] = value;

  updateLinkAttribute();
};

const changeMetaIcon = (value: string) => {
  if (!dataStore.currentLink) return;
  dataStore.currentLink.linkType = value;
  updateLink([dataStore.currentLink]);
};

const formatStyleFill = () => {
  if (!dataStore.currentLink) return;
  if (!dataStore.currentLink.style["fill"]) {
    dataStore.currentLink.style["fill"] = "none";
  }
};

const updateLinkStyle = (key: string, value: string | number) => {
  if (!dataStore.currentLink) return;
  dataStore.currentLink.style[key] = value;

  // formatStyleFill();
  attrUpdateLink(dataStore.currentLink);
};

const updateLinkAttribute = () => {
  if (!dataStore.currentLink) return;
  formatStyleFill();
  dataStore.currentLink.linkStyles = JSON.stringify(dataStore.currentLink.style);
  attrUpdateLink(dataStore.currentLink);
  updateLink([dataStore.currentLink]);
};

const updateMetaData = (value: Record<string, string>) => {
  if (!dataStore.currentLink) return;
  dataStore.currentLink.metaData = value;
  updateLink([dataStore.currentLink]);
};
</script>

<style scoped></style>
