import { toRaw } from "vue";
import { SVGPathData } from "svg-pathdata";

import { useDataStore, useMapStore } from "@/stores";
import type { ILink, INode } from "@/types";
import { addNodeLinkList } from "@/utils/http/apis";

import { renewNodesLinks } from "./data";
import { formatLinks, formatNodes } from "./format";

const nodeCloneList: INode[] = [];
const linkCloneList: ILink[] = [];

const cloneNodes = (nodes: INode[]) => {
  if (!nodes.length) return;
  const mapStore = useMapStore();
  nodes.forEach((node) => {
    const newNode = window.structuredClone(toRaw(node));
    newNode.nodeId = "";
    newNode.mapId = mapStore.mapInfo!.mapId;
    newNode.targets = [];
    newNode.sources = [];
    newNode.svgData = "";
    newNode.sublayerList = [];
    nodeCloneList.push(newNode);
  });
};

const cloneLinks = (links: ILink[]) => {
  if (!links.length) return;
  const mapStore = useMapStore();

  links.forEach((link) => {
    const newLink = window.structuredClone(toRaw(link));
    newLink.linkId = "";
    newLink.mapId = mapStore.mapInfo!.mapId;
    newLink.target = null;
    newLink.source = null;
    newLink.sublayerList = [];
    linkCloneList.push(newLink);
  });
};

const clearCloneData = () => {
  nodeCloneList.length = 0;
  linkCloneList.length = 0;
};

export const cloneNodesLinks = () => {
  clearCloneData();
  const dataStore = useDataStore();
  if (!dataStore.nodesSelected.length && !dataStore.linksSelected.length) {
    window.$message.warning("请先选择节点或连线");
    return;
  }
  console.log("复制", dataStore.nodesSelected, dataStore.linksSelected);

  cloneNodes(dataStore.nodesSelected);
  cloneLinks(dataStore.linksSelected);
  window.$message.success("元素已复制");
};

const resetNodeLinkPosition = (isMousePaste: boolean) => {
  if (isMousePaste) {
    const mapStore = useMapStore();
    const { x, y } = mapStore.pasteMousePosition;
    const left = Math.min(...[...nodeCloneList, ...linkCloneList].map((node) => node.x));
    const top = Math.min(...[...nodeCloneList, ...linkCloneList].map((node) => node.y));

    nodeCloneList.forEach((node) => {
      node.x = node.x - left + x;
      node.y = node.y - top + y;
      node.nodePosition = `${node.x},${node.y}`;
    });
    linkCloneList.forEach((link) => {
      const d1 = new SVGPathData(link.linkPath);
      link.linkPath = d1
        .translate(x - left, y - top)
        .toAbs()
        .encode();
      link.points = new SVGPathData(link.linkPath).commands;
    });
  } else {
    // nodeCloneList.forEach((node) => {
    //   node.x += 20;
    //   node.y += 20;
    //   node.nodePosition = `${node.x},${node.y}`;
    // });
    // linkCloneList.forEach((link) => {
    //   const d1 = new SVGPathData(link.linkPath);
    //   link.linkPath = d1.translate(20, 20).toAbs().encode();
    //   link.points = new SVGPathData(link.linkPath).commands;
    // });
  }
};
export const pasteData = async (isMousePaste = false) => {
  const dataStore = useDataStore();

  resetNodeLinkPosition(isMousePaste);
  if (!nodeCloneList.length && !linkCloneList.length) {
    window.$message.warning("请先复制节点或连线");
    return;
  }
  const mapStore = useMapStore();

  // 如果跨图层粘贴，需要重新获取mapId
  const { nodeList, linkList } = await addNodeLinkList({
    nodeList: nodeCloneList.map((node) => ({
      ...node,
      mapId: mapStore.mapInfo!.mapId
    })),
    linkList: linkCloneList.map((link) => ({
      ...link,
      mapId: mapStore.mapInfo!.mapId
    }))
  });
  dataStore.nodesAll.push(...formatNodes(nodeList));
  dataStore.linksAll.push(...formatLinks(linkList));
  renewNodesLinks();
  window.$message.success("粘贴成功");
  // clearCloneData();
};
