import * as d3 from "d3";

import { useDataStore, useMapStore } from "@/stores";
import type { ILink, INode, ISVGG } from "@/types";
import { getImageUrl } from "@/utils/tools";

import { bindPreviewSvgZoom } from "../../event";

import { appendPreviewLink } from "./link";
import { appendPreviewNode, updatePreviewNode } from "./node";

let previewGroup: ISVGG<HTMLElement, HTMLElement>;

export const drawPreview = () => {
  const mapStore = useMapStore();
  if (!mapStore.mapInfo?.mapId) return;

  const previewSvg = d3
    .select<SVGSVGElement, HTMLElement>("#previewSvg")
    .style("width", "100%")
    .style("height", "100%")
    .style("position", "absolute");

  previewSvg.select("#previewGroup").remove();

  previewGroup = previewSvg.append<SVGGElement>("g").attr("id", "previewGroup");

  bindPreviewSvgZoom(previewSvg);
  drawPreviewBg();
  drawPreviewElements();
};

const drawPreviewBg = () => {
  const mapStore = useMapStore();
  const background = mapStore.mapInfo?.background || "#292929";
  if (mapStore.mapInfo?.background.includes("/ftp")) {
    previewGroup
      .append("image")
      .attr("id", "previewChainBg")
      .attr("href", getImageUrl() + background)
      .attr("width", mapStore.mapSize.width)
      .attr("height", mapStore.mapSize.height);
  } else {
    previewGroup
      .append("rect")
      .attr("id", "previewChainBg")
      .attr("width", mapStore.mapSize.width)
      .attr("height", mapStore.mapSize.height)
      .attr("fill", background);
  }
};

const drawPreviewElements = () => {
  const dataStore = useDataStore();

  previewGroup
    .append("g")
    .attr("id", "previewLinkGroup")
    .selectAll<SVGPathElement, ILink>("path.preview-link")
    .data(dataStore.linksAll, (d: ILink) => d.linkId)
    .join(appendPreviewLink);

  previewGroup
    .append("g")
    .attr("id", "previewNodeGroup")
    .selectAll<SVGGElement, INode>("g.preview-node-group")
    .data(dataStore.nodesAll, (d: INode) => d.nodeId)
    .join(appendPreviewNode, updatePreviewNode);
};
