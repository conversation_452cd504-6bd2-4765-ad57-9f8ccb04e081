<template>
  <g id="linkDragPointGroup" v-if="dataStore.linksSelected.length === 1">
    <circle
      v-for="(point, index) in points"
      :data-index="index"
      :key="index"
      :transform="transform"
      r="8"
      fill="#63e2b7"
      :cx="getX(point)"
      :cy="getY(point)"
    />
  </g>
</template>

<script setup lang="ts">
import { computed, nextTick, ref, watchEffect } from "vue";
import { SVGPathData } from "svg-pathdata";
import type { SVGCommand } from "svg-pathdata/dist/types";

import { useDataStore } from "@/stores";
import { bindLinkDragPointEvent } from "@/utils/editor/event";

const dataStore = useDataStore();

const points = ref<SVGCommand[]>([]);

watchEffect(() => {
  if (dataStore.nodesSelected.length === 0 && dataStore.linksSelected.length === 1) {
    points.value = dataStore.linksSelected[0].points;
    nextTick(() => {
      bindLinkDragPointEvent(points.value);
    });
  }
});

const transform = computed(() => {
  return `translate(${dataStore.linksSelected[0].transform.x}, ${dataStore.linksSelected[0].transform.y})`;
});

const getX = (point: SVGCommand) => {
  if (point.type === SVGPathData.CLOSE_PATH) {
    return 0;
  }
  if (point.type === SVGPathData.VERT_LINE_TO) {
    return point.y;
  }
  return point.x;
};

const getY = (point: SVGCommand) => {
  if (point.type === SVGPathData.CLOSE_PATH) {
    return 0;
  }
  if (point.type === SVGPathData.HORIZ_LINE_TO) {
    return point.x;
  }
  return point.y;
};
</script>

<style></style>
