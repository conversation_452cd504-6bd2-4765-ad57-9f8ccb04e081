<template>
  <n-dropdown size="small" :options="menuOptions" @select="handleSelect">
    <n-button quaternary size="small" class="mr-1">
      <template #icon>
        <n-icon><MenuSharp /></n-icon>
      </template>
    </n-button>
  </n-dropdown>
</template>

<script setup lang="ts">
import { type Component, computed, h } from "vue";
import { useRoute, useRouter } from "vue-router";
import { type MenuOption, NIcon } from "naive-ui";

import { MenuSharp } from "@/utils/components/icons";

const route = useRoute();
const router = useRouter();

function renderIcon(icon: Component) {
  return () => h(NIcon, null, { default: () => h(icon) });
}

const menuOptions: MenuOption[] = [
  {
    label: "编辑工具",
    key: "Editor"
  },
  // {
  //   label: "画布组合",
  //   key: "Combine",
  //   icon: renderIcon(CombineIcon)
  // },
  {
    label: "图元管理",
    key: "MetaIcon"
  },
  {
    label: "权限管理",
    key: "Permission"
  },
  {
    label: "系统管理",
    key: "System"
  }
];

const handleSelect = (value: string) => {
  router.push({ name: value });
};
</script>

<style></style>
