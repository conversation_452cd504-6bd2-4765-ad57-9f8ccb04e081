<template>
  <BaseGrid title="图元">
    <n-select
      :value="type"
      filterable
      placeholder="选择图元"
      size="small"
      :options="metaStore.metaOptions"
      @update:value="onMetaChange"
    />
  </BaseGrid>
</template>

<script setup lang="ts">
import { useMetaStore } from "@/stores";
import type { IMetaItem } from "@/types";

import BaseGrid from "@/components/Common/BaseGrid/index.vue";

const emit = defineEmits<{
  "update:bindMeta": [value: string, row: IMetaItem];
}>();

defineProps<{
  type?: string;
}>();

const metaStore = useMetaStore();

const onMetaChange = (value: string, { row }: { row: IMetaItem }) => {
  emit("update:bindMeta", value, row);
};
</script>

<style></style>
