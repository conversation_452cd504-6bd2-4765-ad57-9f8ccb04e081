import Image from "@/assets/images/meta/Image.svg?url";
import type { INode } from "@/types";

export const getImageUrl = () => {
  const { origin } = window.location;
  return origin.includes("localhost") ? "http://**************:6818" : origin;
};
export const getNodeImage = (node: INode) => {
  const urlPrefix = getImageUrl();
  const imageUrl = node.style.image || node.objImg;
  const url = urlPrefix + imageUrl;
  return imageUrl ? url : Image;
};
