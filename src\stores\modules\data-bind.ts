import { ref } from "vue";
import { defineStore } from "pinia";

import type { IDataExtract } from "@/types";
import {
  getDataExtract,
  getDataExtractInfo as getDataExtractInfoByHttp,
  getDetailByExtractId
} from "@/utils/http/apis/";
interface ISelectionOption {
  label: string;
  value: string;
}

export const useDataBindStore = defineStore("data-bind", () => {
  const dataExtractList = ref<IDataExtract[]>([]);
  const dataExtractInfoList = ref<Record<string, any>[]>([]);
  const dataExtractDetailOptions = ref<ISelectionOption[]>([]);
  const dataExtractKeyOptions = ref<ISelectionOption[]>([]);

  const getDataExtractList = async () => {
    dataExtractList.value = await getDataExtract();
  };

  const params: Record<string, any> = {};

  const getExtractDetail = async (extractId: number) => {
    const detail = await getDetailByExtractId(extractId);
    const { detailList, dmTargetDataExtract } = detail;
    const intfcParamList = JSON.parse(dmTargetDataExtract.intfcParam);

    intfcParamList.forEach((item: { type: string; key: string; value: string }) => {
      params[item.key] = {
        type: item.type,
        value: item.value
      };
    });

    dataExtractDetailOptions.value = Object.entries(detailList).map(([key, value]) => {
      return {
        label: value.name,
        value: value.id
      };
    });
  };

  const getDataExtractInfo = async (id: number) => {
    dataExtractInfoList.value = await getDataExtractInfoByHttp(id, params);

    if (!dataExtractInfoList.value.length) return;
    dataExtractKeyOptions.value = Object.entries(dataExtractInfoList.value[0]).map(([key]) => {
      return {
        label: key,
        value: key
      };
    });
  };

  return {
    dataExtractList,
    dataExtractKeyOptions,
    dataExtractDetailOptions,
    dataExtractInfoList,
    getDataExtractList,
    getDataExtractInfo,
    getExtractDetail
  };
});
