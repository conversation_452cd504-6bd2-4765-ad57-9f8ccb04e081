<template>
  <FillColor @update:value="updateNodeStyle" @complete="updateNodeAttribute"></FillColor>
</template>

<script setup lang="ts">
import FillColor from "@/components/SvgEditor/Panel/Right/Attribute/Item/Common/FillColor.vue";
import { useDataStore } from "@/stores";
import { updateNode } from "@/utils/http/apis";
const dataStore = useDataStore();

const updateNodeStyle = (key: string, value: string | number) => {
  dataStore.nodesSelected.forEach((node) => {
    node.style[key] = value;
  });
};

const updateNodeAttribute = () => {
  updateNode(dataStore.nodesSelected);
};
</script>

<style scoped></style>
