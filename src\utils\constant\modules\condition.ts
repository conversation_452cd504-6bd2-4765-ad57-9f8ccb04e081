import type { SelectOption } from "naive-ui";

import type { IStyleOption } from "@/types";

export const DataTypeOptions = [
  { label: "文本", value: "text" },
  { label: "数字", value: "number" },
  { label: "布尔", value: "boolean" }
];

export const ComparisonOptions = [
  { label: "大于", value: ">" },
  { label: "小于", value: "<" },
  { label: "等于", value: "=" },
  { label: "大于等于", value: ">=" },
  { label: "小于等于", value: "<=" }
];

export const StyleTypeNodeOptions: IStyleOption[] = [
  { label: "背景", value: "background", type: "image" },
  // { label: "主题", value: "theme", type: "select" },
  // { label: "尺寸", value: "size", type: "select" },
  // { label: "值", value: "value", type: "number" },
  { label: "文本", value: "text", type: "text" },
  { label: "填充", value: "fill", type: "color" },
  { label: "动画", value: "animation", type: "select" },
  // { label: "描边", value: "stroke", type: "color" },
  // { label: "描边宽度", value: "stroke-width", type: "number" },
  // { label: "颜色", value: "color", type: "color" },
  { label: "缩放", value: "scale", type: "number" },
  // { label: "宽度", value: "width", type: "number" },
  // { label: "高度", value: "height", type: "number" },
  // { label: "半径", value: "radius", type: "number" },
  { label: "可见性", value: "display", type: "select" },
  { label: "透明度", value: "opacity", type: "number" }
];

export const StyleTypeLinkOptions: IStyleOption[] = [
  { label: "描边", value: "stroke", type: "color" },
  { label: "描边宽度", value: "stroke-width", type: "number" }
];

export const StyleSelectMap: Record<string, SelectOption[]> = {
  animation: [
    {
      label: "闪烁",
      value: "blink"
    }
  ],
  theme: [
    {
      label: "红",
      value: "red"
    },
    {
      label: "绿",
      value: "green"
    },
    {
      label: "蓝",
      value: "blue"
    },
    {
      label: "橙",
      value: "orange"
    },
    {
      label: "黄",
      value: "yellow"
    },
    {
      label: "紫",
      value: "purple"
    },
    {
      label: "黑",
      value: "black"
    },
    {
      label: "白",
      value: "white"
    },
    {
      label: "灰",
      value: "gray"
    }
  ],
  size: [
    {
      label: "大",
      value: "large"
    },
    {
      label: "中",
      value: "medium"
    },
    {
      label: "小",
      value: "small"
    }
  ],
  display: [
    {
      label: "可见",
      value: "block"
    },
    {
      label: "不可见",
      value: "none"
    }
  ]
};
