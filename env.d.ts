/// <reference types="vite/client" />
/// <reference types="vite-svg-loader" />

import type { MessageApiInjection } from "naive-ui/es/message/src/MessageProvider";

declare global {
  //   window.WisActionLoader.executeCommand(resourceId,commandName)
  interface Window {
    $message: MessageApiInjection;
    WisActionLoader?: {
      executeCommand(resourceId: string, commandName: string): void;
    };
  }
}
