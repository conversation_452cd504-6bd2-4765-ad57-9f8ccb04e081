<template>
  <div class="w-full h-8 flex flex-shrink-0 justify-between items-center px-1">
    <div class="flex">
      <Navigation></Navigation>
      <slot name="left"> </slot>
    </div>
    <div class="flex">
      <slot name="middle"> </slot>
    </div>
    <div class="flex">
      <slot name="right"> </slot>
      <n-dropdown size="small" :options="settingOptions" @select="handleSelect">
        <!-- <n-button quaternary size="small" class="mr-1"> 设置 </n-button> -->
        <n-button quaternary size="small">
          <template #icon>
            <n-icon size="20">
              <UserAvatarFilledAlt />
            </n-icon>
          </template>
        </n-button>
      </n-dropdown>
    </div>
  </div>
  <MapVersion ref="mapVersionRef" />
</template>

<script setup lang="ts">
import { ref } from "vue";

import MapVersion from "@/components/Version/index.vue";
import { useCommonStore } from "@/stores";
import { UserAvatarFilledAlt } from "@/utils/components/icons";
import Navigation from "@/components/Common/Navigation/index.vue";

const commonStore = useCommonStore();
const mapVersionRef = ref<InstanceType<typeof MapVersion> | null>(null);

const settingOptions = [
  {
    label: "版本信息",
    key: "version"
  },
  {
    label: "退出登录",
    key: "logout"
  }
];

const toggleCollapsed = () => {
  commonStore.collapsed = !commonStore.collapsed;
};

const handleSelect = (key: string) => {
  switch (key) {
    case "version":
      mapVersionRef.value?.show();
      break;
    // case "logout":
    //   window.$router.push("/login");
    //   break;
  }
};
</script>

<style scoped></style>
