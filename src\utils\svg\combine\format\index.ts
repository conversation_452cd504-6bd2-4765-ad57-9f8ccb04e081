import * as d3 from "d3";
import { SVGPathData } from "svg-pathdata";

import type { ICombineSvgData } from "@/types";

import { groupTransform } from "../event/svg";

export const getSvgSize = () => {
  const groupRect = d3.select<SVGGElement, any>(".combine-group").node()?.getBoundingClientRect();
  if (!groupRect)
    return {
      width: 100,
      height: 100
    };

  const width = Math.round(groupRect.width / groupTransform.k);
  const height = Math.round(groupRect.height / groupTransform.k);

  return { width, height };
};

export const getSvgNodesLinks = () => {
  const groupRect = d3.select<SVGGElement, any>(".combine-group").node()?.getBoundingClientRect();
  const groupLeft = groupRect?.x || 0;
  const groupTop = groupRect?.y || 0;

  const svgs: ICombineSvgData[] = [];

  d3.select<SVGGElement, any>(".combine-group")
    .selectAll<SVGAElement, ICombineSvgData>(".combine-svg-item")
    .each(function (d) {
      const data = window.structuredClone(d);
      const rect = this.getBoundingClientRect();
      const x = (rect.x - groupLeft) / groupTransform.k;
      const y = (rect.y - groupTop) / groupTransform.k;

      if (x !== 0 || y !== 0) {
        data.nodes.forEach((node) => {
          node.x += x;
          node.y += y;
          node.nodePosition = `${node.x},${node.y}`;
        });
        data.links.forEach((link) => {
          const d1 = new SVGPathData(link.linkPath);
          const path = d1.matrix(1, 0, 0, 1, x, y).toAbs().encode();
          link.linkPath = path;
        });
      }

      svgs.push(data);
    });

  return svgs;
};

let svgList: ICombineSvgData[] = [];

const alignLeft = () => {
  const leftX = Math.min(...svgList.map((svg: ICombineSvgData) => svg.x));
  svgList.forEach((svg: ICombineSvgData) => {
    svg.x = leftX;
  });
};
// 完成后续代码
const alignRight = () => {
  const rightX = Math.max(...svgList.map((svg: ICombineSvgData) => svg.x + svg.size.width));
  svgList.forEach((svg: ICombineSvgData) => {
    svg.x = rightX - svg.size.width;
  });
};

const alignTop = () => {
  const topY = Math.min(...svgList.map((svg: ICombineSvgData) => svg.y));
  svgList.forEach((svg: ICombineSvgData) => {
    svg.y = topY;
  });
};

const alignBottom = () => {
  const bottomY = Math.max(...svgList.map((svg: ICombineSvgData) => svg.y + svg.size.height));
  svgList.forEach((svg: ICombineSvgData) => {
    svg.y = bottomY - svg.size.height;
  });
};

const alignCenterX = () => {
  const centerX = Math.round(
    svgList.reduce((sum, svg) => sum + svg.x + svg.size.width / 2, 0) / svgList.length
  );
  svgList.forEach((svg: ICombineSvgData) => {
    svg.x = centerX - svg.size.width / 2;
  });
};

const alignCenterY = () => {
  const centerY = Math.round(
    svgList.reduce((sum, svg) => sum + svg.y + svg.size.height / 2, 0) / svgList.length
  );
  svgList.forEach((svg: ICombineSvgData) => {
    svg.y = centerY - svg.size.height / 2;
  });
};

const alignDistributeX = () => {
  const xList = svgList.map((svg: ICombineSvgData) => svg.x);
  const minX = Math.min(...xList);
  const maxX = Math.max(...xList);
  const distributeX = (maxX - minX) / (svgList.length - 1);
  svgList.forEach((svg: ICombineSvgData, index: number) => {
    svg.x = minX + distributeX * index;
  });
};

const alignDistributeY = () => {
  const yList = svgList.map((svg: ICombineSvgData) => svg.y);
  const minY = Math.min(...yList);
  const maxY = Math.max(...yList);
  const distributeY = (maxY - minY) / (svgList.length - 1);
  svgList.forEach((svg: ICombineSvgData, index: number) => {
    svg.y = minY + distributeY * index;
  });
};

export const alignCombineSvg = (type: string, svgs: ICombineSvgData[]) => {
  svgList = svgs;

  switch (type) {
    case "Left":
      alignLeft();
      break;
    case "Right":
      alignRight();
      break;
    case "Top":
      alignTop();
      break;
    case "Bottom":
      alignBottom();
      break;
    case "CenterX":
      alignCenterX();
      break;
    case "CenterY":
      alignCenterY();
      break;
    case "DistributeX":
      alignDistributeX();
      break;
    case "DistributeY":
      alignDistributeY();
      break;
    default:
  }
};
