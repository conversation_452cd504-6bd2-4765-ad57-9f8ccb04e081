<template>
  <rect class="node-text-rect" :width="data.width" :height="data.height" :style="nodeStyles"></rect>
  <Text :data="data" />
  <Divider :data="data" :node-styles="nodeStyles" />
</template>

<script setup lang="ts">
import { computed, onMounted } from "vue";
import * as d3 from "d3";

import type { INode, IStyle } from "@/types";
import { bindNodeDrag } from "@/utils/editor/event";

import Text from "./Text.vue";
import Divider from "./Divider.vue";

// 类型定义
interface NodeStylesResult extends IStyle {
  filter?: string;
}

const props = defineProps<{
  data: INode;
}>();

// 计算属性：节点样式
const nodeStyles = computed((): NodeStylesResult => {
  const style = { ...props.data.style };

  // 确保line属性存在
  if (!style.line) {
    style.line = {
      length: 0,
      rotate: 0
    };
  }

  return style;
});

onMounted(() => {
  const node = d3.select<SVGGElement, INode>(`#node_${props.data.nodeId}`).data([props.data]);

  bindNodeDrag(node);
});
</script>

<style></style>
