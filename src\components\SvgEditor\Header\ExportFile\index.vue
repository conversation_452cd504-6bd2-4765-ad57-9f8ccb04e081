<template>
  <n-modal
    v-model:show="isVisible"
    preset="dialog"
    title="导出设置"
    size="huge"
    :bordered="false"
    :show-icon="false"
    :close-on-esc="false"
    :maskClosable="false"
    positive-text="确认"
    negative-text="取消"
    @positive-click="submit"
    @negative-click="hide"
    style="margin-top: 20vh"
  >
    <div class="flex flex-col">
      <div>选择项</div>
      <div>显示区域</div>
      <div class="flex"></div>
    </div>
  </n-modal>
</template>

<script setup lang="ts">
import { ref } from "vue";

const isVisible = ref(false);
const groupModel = ref({
  groupName: "",
  groupDescription: "",
  groupType: 0
});

const show = () => {
  isVisible.value = true;
};

const hide = () => {
  isVisible.value = false;
  groupModel.value = {
    groupName: "",
    groupDescription: "",
    groupType: 0
  };
};

const submit = () => {
  console.log("submit");
};

defineExpose({
  show
});
</script>
