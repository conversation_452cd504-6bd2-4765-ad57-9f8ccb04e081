<template>
  <!-- <n-form ref="formRef" label-placement="left" inline>
      <n-form-item>
        <n-input placeholder="数据key" v-model:value="dataKey" disabled> </n-input>
        <n-button size="small" class="ml-2" @click="showDataBindModal"> 设置 </n-button>
      </n-form-item>
      <n-form-item>
        <n-select class="w-40" :options="DataTypeOptions" :disabled="!domId" placeholder="数据类型">
        </n-select>
      </n-form-item>
    </n-form> -->

  <div class="flex justify-end mx-2">
    <n-button size="small" type="primary" class="mb-5" @click="addGroupData">新增配置</n-button>
    <!-- <n-button size="small" type="info" class="mr-2" @click="onSaveDataBind">保存</n-button>
      <n-button size="small" type="error" @click="deleteSaveDataBind">删除</n-button> -->
  </div>

  <n-spin :show="isLoading">
    <n-scrollbar style="max-height: 300px" v-if="dataBindForm.length">
      <n-collapse :trigger-areas="['arrow', 'main']">
        <n-collapse-item v-for="(data, dataIndex) in dataBindForm" :key="`data-${dataIndex}`">
          <template #header>
            <span class="w-full cursor-pointer">{{ `key：${data.column || "--"}` }}</span>
          </template>
          <template #header-extra>
            <div>
              <n-tooltip trigger="hover">
                <template #trigger>
                  <n-button
                    text
                    style="font-size: 18px"
                    type="primary"
                    class="mx-1"
                    @click="showDataBindModal(dataIndex)"
                  >
                    <n-icon :component="Settings" />
                  </n-button>
                </template>
                配置数据key
              </n-tooltip>

              <n-tooltip trigger="hover">
                <template #trigger>
                  <n-button
                    text
                    style="font-size: 18px"
                    type="info"
                    class="mx-1"
                    @click="addItem(data)"
                  >
                    <n-icon :component="Add" />
                  </n-button>
                </template>
                新增条件配置
              </n-tooltip>

              <n-tooltip trigger="hover">
                <template #trigger>
                  <n-button
                    text
                    style="font-size: 18px"
                    type="info"
                    class="mx-1"
                    @click="onSaveDataBind(data, dataIndex)"
                  >
                    <n-icon :component="Save" />
                  </n-button>
                </template>
                保存当前项
              </n-tooltip>

              <n-button
                text
                style="font-size: 18px"
                type="error"
                class="mx-1"
                @click="deleteSaveDataBind(data, dataIndex)"
              >
                <n-icon :component="Delete" />
              </n-button>
            </div>
          </template>
          <n-scrollbar style="max-height: 255px">
            <n-form ref="formRef" :model="data.conditions" label-placement="left" class="mr-2">
              <n-form-item v-for="(item, index) in data.conditions" :key="index">
                <n-input-group>
                  <n-select
                    v-model:value="item.style.type"
                    :options="StyleOptions"
                    class="w-40 flex-shrink-0"
                    clearable
                    placeholder="类型"
                    @update:value="onStyleTypeChange($event, item)"
                  >
                  </n-select>
                  <n-input-number
                    v-if="item.style.type !== 'text'"
                    v-model:value="item.threshold"
                    class="w-30 flex-shrink-0"
                    clearable
                    placeholder="阈值"
                  />
                  <n-select
                    v-if="item.style.type !== 'text'"
                    v-model:value="item.comparison"
                    :options="ComparisonOptions"
                    class="w-26 flex-shrink-0"
                  >
                  </n-select>
                  <!-- <n-select
                    v-if="domId"
                    v-model:value="item.tagName"
                    :options="tagNameOptions"
                    clearable
                    placeholder="元素"
                    class="w-22 flex-shrink-0"
                  >
                  </n-select> -->

                  <n-input
                    v-if="getInputType(item.style.type, 'string')"
                    v-model:value="item.style.data"
                    clearable
                    placeholder="请输入样式"
                  />

                  <n-color-picker
                    v-else-if="getInputType(item.style.type, 'color')"
                    v-model:value="item.style.data"
                    :show-preview="true"
                    :modes="['hex', 'rgb']"
                  />
                  <div v-else-if="getInputType(item.style.type, 'image')" class="flex flex-1">
                    <n-input v-model:value="item.style.data" clearable placeholder="请上传图标">
                      <template #suffix>
                        <n-upload
                          accept="image/*"
                          style="height: 22px; width: 22px; margin-left: 5px; margin-bottom: 2px"
                          clearable
                          :default-upload="false"
                          :show-file-list="false"
                          @change="onUploadChange($event, item)"
                        >
                          <n-icon :component="CloudUpload" size="20" class="cursor-pointer" />
                        </n-upload>
                      </template>
                    </n-input>

                    <div
                      class="bg-#424247 flex justify-center items-center ml-2 w-34px h-full flex-shrink-0 rounded-3px"
                    >
                      <n-image width="30" :src="urlPrefix + item.style.data" />
                    </div>
                  </div>
                  <n-input-number
                    v-else-if="getInputType(item.style.type, 'number')"
                    v-model:value="item.style.data"
                    class="flex-1"
                    clearable
                    placeholder="请输入样式"
                  />

                  <n-select
                    v-else-if="getInputType(item.style.type, 'select')"
                    v-model:value="item.style.data"
                    :options="getStyleSelectOptions(item.style.type)"
                    clearable
                    placeholder="请选择样式"
                  >
                  </n-select>
                </n-input-group>
                <n-button text style="font-size: 18px" class="mx-1" @click="addItem(data, index)">
                  <n-icon :component="Add" />
                </n-button>
                <n-button text style="font-size: 18px" @click="removeItem(index, data)">
                  <n-icon :component="Subtract" />
                </n-button>
              </n-form-item>
            </n-form>
          </n-scrollbar>
        </n-collapse-item>
      </n-collapse>
    </n-scrollbar>
    <n-empty description="暂无数据" v-else> </n-empty>
  </n-spin>

  <DataBindModal ref="dataBindRef" @onValueUpdate="onValueUpdate"></DataBindModal>
</template>

<script setup lang="ts">
import { computed, type ComputedRef, ref, toRaw } from "vue";
import { type SelectOption, type UploadFileInfo, useDialog } from "naive-ui";

import DataBindModal from "@/components/Common/Modal/DataBindConfig/index.vue";
import { useMetaStore } from "@/stores";
import type { ICondition, IMetaIconDataBind, IMetaItem, IStyleOption } from "@/types";
import { Add, CloudUpload, Delete, Save, Settings, Subtract } from "@/utils/components/icons";
import {
  ComparisonOptions,
  StyleSelectMap,
  StyleTypeLinkOptions,
  StyleTypeNodeOptions
} from "@/utils/constant";
import {
  addMetaDataBind,
  deleteMetaDataBind,
  updataMetaDataBind,
  uploadFile
} from "@/utils/http/apis";
import { getImageUrl } from "@/utils/tools";
import type { ITreeOption } from "@/utils/tools/data/modules/bind";

const urlPrefix = getImageUrl();

const dialog = useDialog();
const tagNameOptions = ref<SelectOption[]>([]);
const metaStore = useMetaStore();
const iconInfo = ref<IMetaItem>();

const dataBindForm = ref<IMetaIconDataBind[]>([]);
const activeIndex = ref<number>(0);

// const domId = ref<string>();
const styleTypeOption = ref<IStyleOption>();
const dataBindRef = ref<InstanceType<typeof DataBindModal> | null>(null);
const isLoading = ref(false);

const StyleOptions = computed(() => {
  if (iconInfo.value?.compClass === "link") {
    return StyleTypeLinkOptions;
  } else {
    return StyleTypeNodeOptions;
  }
});

// 下拉框选项
const getStyleSelectOptions = (type: string | null) => {
  if (!type) return [];
  return StyleSelectMap[type];
};

const getInputType = (conditionType: string | null, inputType = "string") => {
  let type = "string";
  StyleTypeNodeOptions.forEach((ele) => {
    if (ele.value === conditionType) {
      type = ele.type;
    }
  });

  return type === inputType;
};

const onUploadChange = async (options: { file: UploadFileInfo }, item: ICondition) => {
  const file = options.file.file;
  if (!file) return;
  const formData = new FormData();
  formData.append("file", file);

  if (file.type === "image/svg+xml") {
    const reader = new FileReader();
    reader.onload = function (e) {
      if (e.target) {
        item.style.svgData = e.target.result as string; // 这里是SVG文件的内容
      }
    };
    reader.readAsText(new Blob([file])); // 以文本格式读取文件
  }

  uploadFile(formData)
    .then((res) => {
      item.style.data = res;
    })
    .catch(() => {
      window.$message.error("上传失败");
    });
};

const onStyleTypeChange = (value: string, item: ICondition) => {
  StyleTypeNodeOptions.forEach((ele) => {
    if (ele.value === value) {
      styleTypeOption.value = ele;
    }
  });

  // 将当前图元设为默认数据
  if (value === "background") {
    item.style.data = iconInfo.value?.objImg;
    item.style.svgData = iconInfo.value?.svgData;
  } else {
    item.style.data = null;
  }

  // styleTypeOption.value = options;
};
const showDataBindModal = (index: number) => {
  activeIndex.value = index;
  dataBindRef.value?.show();
};

const onValueUpdate = ({ key, id }: { key: string | null; id: number | null }) => {
  dataBindForm.value[activeIndex.value].column = key || "";
  dataBindForm.value[activeIndex.value].extractId = id;
};

const addGroupData = () => {
  const params = {
    column: "",
    conditions: [],
    extractId: null,
    domId: "",
    objType: iconInfo.value!.objType
  };

  dataBindForm.value.push(params);
};

const addItem = (item: IMetaIconDataBind, index?: number) => {
  const newItem = {
    tagName: null,
    comparison: "=",
    threshold: 0,
    style: {
      data: null,
      type: null
    }
  };
  if (index === undefined) {
    item.conditions?.push(newItem);
  } else {
    item.conditions?.splice(index + 1, 0, newItem);
  }
};

const removeItem = (index: number, item: IMetaIconDataBind) => {
  item.conditions?.splice(index, 1);
};

const getData = async () => {
  isLoading.value = true;
  await metaStore.getMetaIconData(iconInfo.value!.objType);
  isLoading.value = false;
};

const onIdChange = async (val?: ITreeOption) => {
  // domId.value = val ? (val.key as string) : val;
  tagNameOptions.value =
    val?.childrenTagNames.map((ele) => {
      return { label: ele, value: ele };
    }) || [];

  initConditionsForm();
};

const initConditionsForm = () => {
  dataBindForm.value = window.structuredClone(toRaw(metaStore.metaIconDataBindList));
};

const initData = async (iconMeta: IMetaItem) => {
  iconInfo.value = iconMeta;
  await getData();
  initConditionsForm();
};

const deleteSaveDataBind = async (data: IMetaIconDataBind, index: number) => {
  dialog.warning({
    title: "警告",
    content: "确定删除当前数据吗？",
    positiveText: "确定",
    negativeText: "取消",
    maskClosable: false,
    closeOnEsc: false,
    onPositiveClick: async () => {
      if (data.id) {
        await deleteMetaDataBind(data.id);
      }
      dataBindForm.value.splice(index, 1);
      window.$message.success("删除成功");
    },
    onAfterLeave: () => {}
  });
};

const onSaveDataBind = async (data: IMetaIconDataBind, index: number) => {
  if (!data.column) {
    window.$message.warning("请配置数据key");
    return;
  }
  const repeatList = dataBindForm.value.filter((ele) => ele.column === data.column);
  if (repeatList.length > 1) {
    window.$message.warning("数据key重复");
    return;
  }

  if (data.id) {
    await updataMetaDataBind(data);
  } else {
    const item = await addMetaDataBind(data);
    dataBindForm.value[index] = item;
    console.log("🚀 ~ onSaveDataBind ~ dataBindForm.value:", dataBindForm.value);
  }

  const title = data.id ? "修改" : "新增";
  window.$message.success(`${title}成功`);
  // await getData();
  // initConditionsForm();
};

defineExpose({
  onIdChange,
  initData
});
</script>

<style scoped></style>
