import { toRaw } from "vue";
import * as d3 from "d3";

import CloseCircleOutlined from "@/assets/images/icons/common/CloseCircleOutlined.svg?raw";
import Arrow from "@/assets/images/preview/chain/arrow.png";
import chainImage from "@/assets/images/preview/chain/chain_bg.webp";
import ChildNodeBg from "@/assets/images/preview/chain/child_node_bg.png";
import Link from "@/assets/images/preview/chain/link.png";
import NodeBg from "@/assets/images/preview/chain/node_bg.png";
import NodeType_1 from "@/assets/images/preview/chain/type_1.png";
import NodeType_2 from "@/assets/images/preview/chain/type_2.png";
import NodeType_3 from "@/assets/images/preview/chain/type_3.png";
import NodeType_4 from "@/assets/images/preview/chain/type_4.png";
import NodeType_5 from "@/assets/images/preview/chain/type_5.png";
import NodeType_6 from "@/assets/images/preview/chain/type_6.png";
import NodeType_7 from "@/assets/images/preview/chain/type_7.png";
import type { IEnter, INode, ISVGG } from "@/types";
import { sendSyncMessage } from "@/utils/sync";
import { gteTextHeight, gteTextWidth } from "@/utils/tools/import/modules/dagre";

import { bindPreviewChainZoom } from "../../event";

const NodeType = [
  NodeType_1,
  NodeType_2,
  NodeType_3,
  NodeType_4,
  NodeType_5,
  NodeType_6,
  NodeType_7
];

const fontSize = 14;
const Distance = 50;
const ChildHeight = 30;
const ChildTop = 60;
const ChildMargin = 10;

const appendChildNode = (enter: IEnter<string>) => {
  const g = enter
    .append("g")
    .attr("class", "preview-chain-name")
    .attr("transform", (d, i) => `translate(-${Distance / 2 - 5}, ${-(i + 1) * ChildTop})`);

  g.append("image")
    .attr("width", 20)
    .attr("height", 60)
    .attr("x", -20)
    .attr("y", 0)
    .attr("xlink:href", Link);

  g.append("foreignObject")
    .attr("width", 100)
    .attr("height", ChildHeight)
    .append("xhtml:div")
    .style("width", "100px")
    .style("height", `${ChildHeight}px`)
    .style("font-size", "16px")
    .style("color", "#f0f0f0")
    .style("display", "flex")
    .style("align-items", "center")
    .style("justify-content", "center")
    .style("background-image", `url(${ChildNodeBg})`)
    .style("background-size", "100% 100%")
    .style("background-repeat", "no-repeat")
    .html((d) => d);

  return g;
};
const drawChildNodes = (g: ISVGG<INode, SVGGElement>) => {
  g.append("g")
    .attr("class", "preview-chain-node-child-group")
    .each(function (d) {
      const names =
        d.metaData?.linkName?.split("、").filter((ele) => !!ele && ele !== "contains") || [];
      if (!names.length) return;
      d3.select(this)
        .selectAll<SVGGElement, string>("g.preview-chain-name")
        .data(names, (d: string) => d)
        .join(appendChildNode);
    });
};

const getFloatHeight = (d: INode) => {
  return d.metaData?.use
    ? gteTextHeight(
        d.metaData?.use || "",
        12,
        Math.min(gteTextWidth(d.metaData?.use || "", 14), 200)
      ) + 15
    : 0;
};
// 在底部添加浮动节点，对话框的气泡，里面填充文字 为metaData的name
const drawFloatNodes = (g: ISVGG<INode, SVGGElement>) => {
  const con = g
    .append("foreignObject")
    .attr("class", "preview-chain-node-float-group")
    .attr("display", (d) => {
      if (!d.metaData?.use || d.metaData?.use === "NULL") {
        return "none";
      }
      return "block";
    })
    .attr("padding", "5px")
    .attr("width", (d) => Math.min(gteTextWidth(d.metaData?.use || "", 14), 200) + 20)
    .attr("height", (d) => getFloatHeight(d))
    .attr(
      "x",
      (d) => -(Math.min(gteTextWidth(d.metaData?.use || "", 14), 200) + 15) / 2 + d.height / 2
    )
    .attr("y", (d) => d.height + 5)
    .append("xhtml:div")
    .style("width", "100%")
    .style("height", "100%")
    .style("padding-top", "8px");

  con
    .append("div")
    .attr("class", "float-bubble")
    .html((d) => d.metaData?.use || "");
};

const appendPreviewChainNode = (enter: IEnter<INode>) => {
  const g = enter
    .append("g")
    .attr("class", "preview-chain-node-group")
    .attr("id", (d) => `previewChainNode_${d.nodeId}`)
    .attr("transform", (d) => `translate(${d.x}, ${d.y})`);

  g.append("image")
    .attr("width", Distance)
    .attr("height", 20)
    .attr("x", -Distance)
    .attr("y", (d) => d.height / 2 - 10)
    .attr("xlink:href", Arrow)
    .attr("display", (d) => (d.sources.length ? "block" : "none"));

  drawChildNodes(g);
  drawFloatNodes(g);

  const textLayout = g
    .append("foreignObject")
    .attr("width", (d) => d.width)
    .attr("height", (d) => d.height)
    .append("xhtml:div")
    .style("width", (d) => `${d.width}px`)
    .style("height", (d) => `${d.height}px`)
    .style("display", "flex")
    .style("align-items", "center")
    .style("background-image", `url(${NodeBg})`)
    .style("background-size", "100% 100%")
    .style("background-repeat", "no-repeat");

  textLayout
    .append("div")
    .style("width", (d) => d.height + 2 + "px")
    .style("height", (d) => d.height + 2 + "px")
    .style("flex-shrink", "0")
    .style("background-image", (d) => `url(${NodeType[(Number(d.metaData?.type) || 7) - 1]})`)
    .style("background-size", "contain")
    .style("background-repeat", "no-repeat");

  textLayout
    .append("div")
    .attr("class", "preview-chain-node-text")
    .style("font-size", `${fontSize}px`)
    .style("margin-left", "-8px")
    .html((d) => d.nodeText);

  return g;
};

const updatePreviewChainNode = (update: d3.Selection<SVGGElement, INode, SVGGElement, any>) => {
  update.attr("transform", (d) => `translate(${d.x}, ${d.y})`);
  return update;
};

const formatPreviewChainData = (node: INode, nodes: INode[], bgHeight: number) => {
  let width = 0;
  let height = 0;

  let left = ChildMargin + Distance / 2;
  let floatHieght = 0;
  const data = [node, ...nodes].reverse().map((ele) => {
    const d = { ...toRaw(ele) };
    const textWidth = gteTextWidth(d.nodeText, fontSize);
    const textHeight = gteTextHeight(d.nodeText, fontSize, textWidth + 20);

    d.height = textHeight * 1.5;
    d.width = textWidth * 1.5 + d.height;

    d.x = left;
    d.y = 0;
    d.nodePosition = `${d.x},${d.y}`;
    left = d.x + d.width + Distance;

    if (!d.metaData) {
      d.metaData = {};
    }
    if (d.sources[0]) {
      d.metaData.linkName = d.sources[0].metaData?.name || "";
    }

    const linkNames =
      d.metaData?.linkName?.split("、").filter((ele) => !!ele && ele !== "contains") || [];

    floatHieght = Math.max(getFloatHeight(d), floatHieght);
    const h = d.height + linkNames.length * ChildTop;
    height = Math.max(h, height);
    width = left;

    return d;
  });

  data.forEach((ele) => {
    ele.y = height - ele.height;
  });

  height = height + floatHieght + 5;

  console.log("height", height);
  return {
    data,
    width,
    height
  };
};

export const closePreviewChain = () => {
  d3.select("#chainPreviewGroup").remove();
  d3.select("#previewChain").style("display", "none");
};

const drawCLoseButton = (group: ISVGG<SVGGElement, any>, width: number) => {
  const closeBtn = group
    .append("g")
    .attr("class", "preview-chain-close")
    .attr("transform", `translate(${width * 0.975}, 12)`)
    .style("cursor", "pointer")
    .on("click", () => {
      closePreviewChain();
      sendSyncMessage("onHidePreviewChainModal", { type: "svg" });
    })
    .html(CloseCircleOutlined);
  closeBtn
    .select("svg")
    .attr("width", 20)
    .attr("height", 20)
    .style("pointer-events", "bounding-box");
};

const setContentPosition = (bgWidth: number, bgHeight: number, width: number, height: number) => {
  const layoutWidth = bgWidth - 40;
  const layoutHeight = bgHeight - 60;
  const k = Math.min(layoutWidth / width, layoutHeight / height);
  const x = (bgWidth - width * k) / 2;
  const y = (bgHeight - height * k) / 2;
  console.log("🚀 ~ setContentPosition ~ y:", bgHeight, height, height * k, k, y);
  d3.select("#previewChainNodeGroup").attr(
    "transform",
    `translate(${x + 20},${y + 10}) scale(${k})`
  );
};
export const drawPreviewChain = (node: INode, nodes: INode[]) => {
  d3.select("#chainPreviewGroup").remove();
  const bgWidth = (document.querySelector("#previewLayout")?.clientWidth || 1600) - 40;
  // 4.184 长宽比
  const ratio = 4.184;
  const bgHeight = bgWidth / ratio;
  const { data, height, width } = formatPreviewChainData(node, nodes, bgHeight);

  //   在previewGroup中添加弹窗显示，宽度为当前屏幕宽度的一半，高度为当前屏幕高度的一半
  const group = d3
    .select<SVGGElement, any>("#previewChain")
    .style("display", "block")
    .append("g")
    .attr("id", "chainPreviewGroup");

  group
    .append("image")
    .attr("xlink:href", chainImage)
    .attr("width", bgWidth)
    .attr("height", bgHeight);

  group
    .append("g")
    .attr("id", "previewChainNodeGroup")
    .selectAll<SVGGElement, INode>("g.preview-chain-node-group")
    .data(data, (d: INode) => d.nodeId)
    .join(appendPreviewChainNode, updatePreviewChainNode);

  drawCLoseButton(group, bgWidth);
  bindPreviewChainZoom(bgWidth, bgHeight);
  setContentPosition(bgWidth, bgHeight, width, height);
};
