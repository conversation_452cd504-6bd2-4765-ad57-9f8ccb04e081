export interface IMetaItem {
  index?: number;
  objType: string;
  objName: string;
  groupId?: string;
  groupName?: string;
  objImg: string;
  objImgUrl?: string;
  compClass?: string;
  script?: string;
  imgScale?: string;
  updatedBy?: string;
  updatedTime?: string;
  svgData?: string;
}

export interface IMetaSource {
  groupId: string;
  groupName: string;
  objList: IMetaItem[];
}

export interface IGroupModel {
  groupId: string;
  groupName: string;
}

export interface IMetaModel {
  objType: string;
  objName: string;
  groupId: string;
  compClass: string;
  script: string;
  objImg: string;
  objImgUrl?: string;
  imgScale: string;
  svgData?: string;
}

export interface ICondition {
  comparison: string;
  tagName: string | null;
  threshold: string | number;
  style: {
    data?: string | number | null;
    type: string | null;
    svgData?: string | null;
  };
}

export interface IMetaIconDataBind {
  id?: number;
  objType: string;
  domId?: string;
  column: string;
  dataType?: "text" | "number" | "boolean";
  extractId: number | null;
  conditions?: ICondition[];
}

export type IStyleOption = {
  label: string;
  value: string;
  type: "image" | "color" | "select" | "string" | "number" | "text"; // Specify the type as either string or number
};

export interface IScriptSource extends IScriptItem {
  id: number;
  createTime?: string;
  updateTime?: string;
  createBy?: string;
  updateBy?: string;
  timestamp?: number;
}

export interface IScriptItem {
  scriptName: string;
  mapId: string;
  script: string;
  status: boolean;
}
