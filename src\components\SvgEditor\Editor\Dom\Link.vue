<template>
  <g :id="`link_${data.linkId}`" :transform="transform" class="link cursor-pointer">
    <path
      :d="linkPath"
      :stroke-width="data.linkWidth"
      :style="data.style"
      :marker-end="mapStore.isLinkArrowVisible ? `url(#arrow)` : ''"
    />
  </g>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, watch } from "vue";
import * as d3 from "d3";
import { SVGPathData } from "svg-pathdata";

import { useMapStore } from "@/stores";
import type { ILink } from "@/types";
import { bindLinkDrag } from "@/utils/editor/event";

const mapStore = useMapStore();

const props = defineProps<{
  data: ILink;
}>();

const emit = defineEmits<{
  "update-bbox": [bbox: DOMRect];
}>();

const transform = computed(() => {
  return `translate(${props.data.transform.x}, ${props.data.transform.y})`;
});

const linkPath = computed(() => {
  return new SVGPathData(props.data.points).encode();
});

onMounted(() => {
  const link = d3.select<SVGGElement, ILink>(`#link_${props.data.linkId}`).data([props.data]);
  const bbox = link.node()?.getBBox();
  if (bbox) {
    emit("update-bbox", bbox);
  }
  bindLinkDrag(link);
});

watch(
  () => props.data.points,
  () => {
    nextTick(() => {
      const bbox = d3.select<SVGGElement, ILink>(`#link_${props.data.linkId}`).node()?.getBBox();
      bbox && emit("update-bbox", bbox);
    });
  },
  {
    deep: true
  }
);
</script>

<style></style>
