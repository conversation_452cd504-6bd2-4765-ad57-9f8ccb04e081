<template>
  <!-- 分排线 -->
  <line
    v-if="dividerProps.isVisible"
    :transform="dividerProps.transform"
    :x1="dividerProps.startX"
    :y1="dividerProps.y"
    :x2="dividerProps.endX"
    :y2="dividerProps.y"
    stroke-width="3"
    :stroke="dividerProps.color"
  ></line>
</template>

<script setup lang="ts">
import { computed } from "vue";
import type { INode, IStyle } from "@/types";
import { useConfigStore } from "@/stores";

// 颜色列表 - 用于常规颜色值
const colorList: Record<string, string> = {
  red: "#ff0000",
  green: "#00ff00",
  blue: "#0000ff",
  orange: "#ffa500",
  yellow: "#ffff00",
  purple: "#800080",
  black: "#000000",
  white: "#ffffff",
  gray: "#808080"
};

// 类型定义
interface NodeStylesResult extends IStyle {
  filter?: string;
}

const props = defineProps<{
  data: INode;
  nodeStyles: NodeStylesResult;
}>();

const configStore = useConfigStore();

// 计算属性：分排相关属性
const dividerProps = computed(() => {
  const { style, width, height } = props.data;
  const lineStyle = props.nodeStyles.line;

  // 判断是否显示分排线
  const isVisible = !!style.line && style.line.length > 0 && configStore.isPlanDividerVisible;
  if (!isVisible) {
    return {
      isVisible: false,
      color: "#000000",
      y: 0,
      startX: 0,
      endX: 0,
      transform: ""
    };
  }

  // 计算分排线的各项属性
  const color = style.line?.fill || colorList[style.dynamicColor || ""] || "#000000";
  const y = height / 2;
  const length = lineStyle?.length || 0;
  const startX = width / 2 - length / 2;
  const endX = width / 2 + length / 2;
  const rotate = lineStyle?.rotate || 0;
  const transform = `rotate(${rotate} ${width / 2} ${height / 2})`;

  return {
    isVisible,
    color,
    y,
    startX,
    endX,
    transform
  };
});
</script>
