import * as d3 from "d3";
import { SVGPathData } from "svg-pathdata";
import type { CommandM } from "svg-pathdata/dist/types";

import { useCommonStore, useDataStore, useMapStore, useSvgStore } from "@/stores";
import type { ILink, INode, ISVGG } from "@/types";
import { updateNodesLinks } from "@/utils/http/apis";
import { setNodeSelected } from "@/utils/tools";

import { operationManager } from "../../data/operationManager";

const startPoint = {
  x: 0,
  y: 0
};

let tx = 0;
let ty = 0;
let linkUpdateedList: ILink[] = [];

const dragStart = (e: any, d: INode) => {
  const commonStore = useCommonStore();
  const svgStore = useSvgStore();
  if (commonStore.isSpaceDown) return;

  setNodeSelected(d);

  operationManager.preSelect({
    nodes: [d],
    links: Array.from(new Set([...d.sources, ...d.targets]))
  });

  if (!svgStore.editable) return;
  startPoint.x = e.x;
  startPoint.y = e.y;
};

const dragging = (e: any, d: INode) => {
  const commonStore = useCommonStore();
  const svgStore = useSvgStore();

  if (!svgStore.editable || commonStore.isSpaceDown) return;

  tx = e.x - startPoint.x;
  ty = e.y - startPoint.y;
  d.x = e.x;
  d.y = e.y;

  if (tx === 0 && ty === 0) return;

  // 更新关联的线路
  d.targets.forEach((link) => {
    link.linkPath = new SVGPathData(link.linkPath).translate(e.dx, e.dy).toAbs().encode();
    const lastPoint = {
      ...link.points[link.points.length - 1]
    };
    link.points = new SVGPathData(link.linkPath).toAbs().commands;
    link.points[link.points.length - 1] = lastPoint;
  });
  d.sources.forEach((link) => {
    const point0 = link.points[link.points.length - 1] as CommandM;
    link.points[link.points.length - 1] = { ...point0, x: point0.x + e.dx, y: point0.y + e.dy };
  });
  linkUpdateedList = [...d.sources, ...d.targets];
};

const dragEnd = (e: any, d: INode) => {
  const commonStore = useCommonStore();
  const svgStore = useSvgStore();
  //   表示节点没有移动
  if (tx === 0 && ty === 0) return;
  if (!svgStore.editable || commonStore.isSpaceDown) return;

  //   更新接口
  updateNodesLinks({ nodes: [d], links: Array.from(new Set(linkUpdateedList)) });
  linkUpdateedList = [];

  startPoint.x = 0;
  startPoint.y = 0;
  tx = 0;
  ty = 0;
};

export const bindNodeDrag = (nodeG: ISVGG<INode, SVGGElement | HTMLElement>) => {
  const mapStore = useMapStore();
  const dataStore = useDataStore();

  const drag = d3
    .drag<SVGGElement, INode>()
    .on("start", dragStart)
    .on("drag", dragging)
    .on("end", dragEnd);

  nodeG.call(drag);

  nodeG
    .on("mouseenter", (e, d) => {
      dataStore.previewNode = d;
      dataStore.alignBaseNode = d;
    })
    .on("mouseleave", () => {
      dataStore.previewNode = null;
    })
    .on("mousedown", (e, d) => {
      dragStart(e, d);
    })
    .on("contextmenu", (e) => {
      e.preventDefault();
      e.stopPropagation();
      mapStore.showMapMenu({ x: e.clientX, y: e.clientY }, "node");
    });
};
