import * as d3 from "d3";

import type { ICombineSvgData } from "@/types";
import emitter from "@/utils/mitt";

export const bindCombineNodeEvent = (mapId: string, svgData: ICombineSvgData) => {
  const drag = d3.drag<SVGGElement, ICombineSvgData>().on("drag", function (event, d) {
    d.x = event.x;
    d.y = event.y;
    // d3.select(this).attr("transform", `translate(${d.x} ${d.y})`);
  });
  d3.select<SVGGElement, ICombineSvgData>(`#combineGroup_${mapId}`)
    .datum(svgData)
    .call(drag)
    .on("click", function (event, d) {
      emitter.emit("on:CombineNodeClick", d.mapId);
    });
};
