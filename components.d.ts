/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Add: typeof import('./src/components/SvgEditor/Modal/Group/Add.vue')['default']
    Attribute: typeof import('./src/components/SvgEditor/Panel/Right/Attribute/index.vue')['default']
    Background: typeof import('./src/components/SvgEditor/Panel/Right/Attribute/NodeAttribute/Background/index.vue')['default']
    BackgroundPosition: typeof import('./src/components/SvgEditor/Panel/Right/Attribute/Item/Image/BackgroundPosition.vue')['default']
    BackgroundSize: typeof import('./src/components/SvgEditor/Panel/Right/Attribute/Item/Image/BackgroundSize.vue')['default']
    BackgroundUrl: typeof import('./src/components/SvgEditor/Panel/Right/Attribute/Item/Image/BackgroundUrl.vue')['default']
    BaseGrid: typeof import('./src/components/Common/BaseGrid/index.vue')['default']
    BaseHeader: typeof import('./src/components/Common/BaseHeader/index.vue')['default']
    BaseItem: typeof import('./src/components/Common/BaseItem/index.vue')['default']
    BaseMultiAttribute: typeof import('./src/components/SvgEditor/Panel/Right/Attribute/MultiAttribute/BaseMultiAttribute/index.vue')['default']
    BaseView: typeof import('./src/components/Common/BaseView/index.vue')['default']
    BindMeta: typeof import('./src/components/SvgEditor/Panel/Right/Attribute/Item/BindMeta.vue')['default']
    CodeMirror: typeof import('./src/components/SvgEditor/Panel/Left/CodeMirror/index.vue')['default']
    CombineMap: typeof import('./src/components/Combine/Modal/CombineMap.vue')['default']
    CommandList: typeof import('./src/components/SvgEditor/Panel/Right/Modal/Interaction/CommandList.vue')['default']
    CondintionList: typeof import('./src/components/MetaIcon/Modal/MetaIconData/CondintionList.vue')['default']
    ConditionConfig: typeof import('./src/components/SvgEditor/Panel/Right/DataBind/Modal/ConditionConfig.vue')['default']
    Config: typeof import('./src/components/SvgEditor/Header/Config/index.vue')['default']
    Container: typeof import('./src/components/SvgEditor/Editor/Container.vue')['default']
    CustomItem: typeof import('./src/components/SvgEditor/Panel/Right/Attribute/Item/CustomItem.vue')['default']
    DataBind: typeof import('./src/components/SvgEditor/Panel/Right/DataBind/index.vue')['default']
    DataBindConfig: typeof import('./src/components/Common/Modal/DataBindConfig/index.vue')['default']
    DataBindList: typeof import('./src/components/SvgEditor/Panel/Right/DataBind/DataBindList.vue')['default']
    Divider: typeof import('./src/components/SvgEditor/Editor/Dom/Divider.vue')['default']
    DragAssistant: typeof import('./src/components/SvgEditor/Editor/DragAssistant.vue')['default']
    Edit: typeof import('./src/components/SvgEditor/Modal/Group/Edit.vue')['default']
    EditGroup: typeof import('./src/components/MetaIcon/Modal/EditGroup.vue')['default']
    EditMeta: typeof import('./src/components/MetaIcon/Modal/EditMeta.vue')['default']
    Editor: typeof import('./src/components/SvgEditor/Editor/index.vue')['default']
    EditScript: typeof import('./src/components/SvgEditor/Modal/Script/EditScript.vue')['default']
    Ellipse: typeof import('./src/components/SvgEditor/Editor/Dom/Ellipse.vue')['default']
    EventBind: typeof import('./src/components/SvgEditor/Panel/Right/EventBind/index.vue')['default']
    ExportFile: typeof import('./src/components/SvgEditor/Header/ExportFile/index.vue')['default']
    ExportMeta: typeof import('./src/components/MetaIcon/Modal/ExportMeta.vue')['default']
    FillColor: typeof import('./src/components/SvgEditor/Panel/Right/Attribute/Item/Common/FillColor.vue')['default']
    Filter: typeof import('./src/components/SvgEditor/Panel/Right/EventBind/Filter/index.vue')['default']
    FilterForm: typeof import('./src/components/SvgEditor/Panel/Right/EventBind/Filter/FilterForm.vue')['default']
    FlexLayout: typeof import('./src/components/SvgEditor/Panel/Right/Attribute/Item/Node/FlexLayout.vue')['default']
    FontFamily: typeof import('./src/components/SvgEditor/Panel/Right/Attribute/Item/Text/FontFamily.vue')['default']
    FontWeight: typeof import('./src/components/SvgEditor/Panel/Right/Attribute/Item/Text/FontWeight.vue')['default']
    GroupList: typeof import('./src/components/SvgEditor/Panel/Left/GroupList/index.vue')['default']
    Header: typeof import('./src/components/SvgEditor/Header/index.vue')['default']
    Image: typeof import('./src/components/SvgEditor/Editor/Dom/Image.vue')['default']
    ImportMoveToSublayer: typeof import('./src/components/SvgEditor/Modal/Sublayer/ImportMoveToSublayer.vue')['default']
    ImportType: typeof import('./src/components/SvgEditor/Modal/Menu/ImportType.vue')['default']
    InnerNode: typeof import('./src/components/SvgEditor/Panel/Right/Attribute/Item/Link/InnerNode.vue')['default']
    Interaction: typeof import('./src/components/SvgEditor/Panel/Right/EventBind/Interaction/index.vue')['default']
    Item: typeof import('./src/components/SvgEditor/Panel/Right/Attribute/Item/index.vue')['default']
    Left: typeof import('./src/components/SvgEditor/Panel/Left/index.vue')['default']
    LineHeight: typeof import('./src/components/SvgEditor/Panel/Right/Attribute/Item/Text/LineHeight.vue')['default']
    Link: typeof import('./src/components/SvgEditor/Editor/Dom/Link.vue')['default']
    LinkAttribute: typeof import('./src/components/SvgEditor/Panel/Right/Attribute/LinkAttribute/index.vue')['default']
    LinkDraft: typeof import('./src/components/SvgEditor/Editor/Assistant/LinkDraft.vue')['default']
    LinkDragPoint: typeof import('./src/components/SvgEditor/Editor/LinkDragPoint.vue')['default']
    LinkItem: typeof import('./src/components/SvgEditor/Panel/Right/Attribute/MapAttribute/Items/LinkItem.vue')['default']
    LinkMultiAttribute: typeof import('./src/components/SvgEditor/Panel/Right/Attribute/MultiAttribute/LinkMultiAttribute/index.vue')['default']
    LinkPoints: typeof import('./src/components/SvgEditor/Panel/Right/Attribute/LinkAttribute/Items/LinkPoints.vue')['default']
    MapAttribute: typeof import('./src/components/SvgEditor/Panel/Right/Attribute/MapAttribute/index.vue')['default']
    MapBackground: typeof import('./src/components/SvgEditor/Panel/Right/Attribute/MapAttribute/Items/MapBackground.vue')['default']
    MapZoom: typeof import('./src/components/SvgEditor/Panel/Right/Attribute/MapAttribute/Items/MapZoom.vue')['default']
    MenuEdit: typeof import('./src/components/SvgEditor/Modal/Menu/MenuEdit.vue')['default']
    MenuList: typeof import('./src/components/SvgEditor/Panel/Left/MenuList/index.vue')['default']
    MergeList: typeof import('./src/components/SvgEditor/Panel/Left/MergeList/index.vue')['default']
    MetaData: typeof import('./src/components/SvgEditor/Panel/Right/Attribute/Item/MetaData.vue')['default']
    MetaDataConfig: typeof import('./src/components/SvgEditor/Modal/Attribute/MetaDataConfig/index.vue')['default']
    MetaIconData: typeof import('./src/components/MetaIcon/Modal/MetaIconData/index.vue')['default']
    MetaIconList: typeof import('./src/components/SvgEditor/Panel/Left/MetaIconList/index.vue')['default']
    MetaTree: typeof import('./src/components/MetaIcon/MetaTree/index.vue')['default']
    ModelData: typeof import('./src/components/SvgEditor/Header/ModelData/index.vue')['default']
    MouseEvent: typeof import('./src/components/SvgEditor/Panel/Right/EventBind/MouseEvent/index.vue')['default']
    MoveToSublayer: typeof import('./src/components/SvgEditor/Modal/Sublayer/MoveToSublayer.vue')['default']
    MultiAttribute: typeof import('./src/components/SvgEditor/Panel/Right/Attribute/MultiAttribute/index.vue')['default']
    Navigation: typeof import('./src/components/Common/Navigation/index.vue')['default']
    NButton: typeof import('naive-ui')['NButton']
    NCard: typeof import('naive-ui')['NCard']
    NCascader: typeof import('naive-ui')['NCascader']
    NCheckbox: typeof import('naive-ui')['NCheckbox']
    NCheckboxGroup: typeof import('naive-ui')['NCheckboxGroup']
    NCollapse: typeof import('naive-ui')['NCollapse']
    NCollapseItem: typeof import('naive-ui')['NCollapseItem']
    NColorPicker: typeof import('naive-ui')['NColorPicker']
    NConfigProvider: typeof import('naive-ui')['NConfigProvider']
    NDataTable: typeof import('naive-ui')['NDataTable']
    NDialogProvider: typeof import('naive-ui')['NDialogProvider']
    NDivider: typeof import('naive-ui')['NDivider']
    NDrawer: typeof import('naive-ui')['NDrawer']
    NDrawerContent: typeof import('naive-ui')['NDrawerContent']
    NDropdown: typeof import('naive-ui')['NDropdown']
    NEmpty: typeof import('naive-ui')['NEmpty']
    NFlex: typeof import('naive-ui')['NFlex']
    NFloatButton: typeof import('naive-ui')['NFloatButton']
    NForm: typeof import('naive-ui')['NForm']
    NFormItem: typeof import('naive-ui')['NFormItem']
    NFormItemGi: typeof import('naive-ui')['NFormItemGi']
    NGi: typeof import('naive-ui')['NGi']
    NGrid: typeof import('naive-ui')['NGrid']
    NGridItem: typeof import('naive-ui')['NGridItem']
    NIcon: typeof import('naive-ui')['NIcon']
    NImage: typeof import('naive-ui')['NImage']
    NInput: typeof import('naive-ui')['NInput']
    NInputGroup: typeof import('naive-ui')['NInputGroup']
    NInputNumber: typeof import('naive-ui')['NInputNumber']
    NLayout: typeof import('naive-ui')['NLayout']
    NLayoutSider: typeof import('naive-ui')['NLayoutSider']
    NList: typeof import('naive-ui')['NList']
    NListItem: typeof import('naive-ui')['NListItem']
    NMessageProvider: typeof import('naive-ui')['NMessageProvider']
    NModal: typeof import('naive-ui')['NModal']
    NModalProvider: typeof import('naive-ui')['NModalProvider']
    NNotificationProvider: typeof import('naive-ui')['NNotificationProvider']
    Node: typeof import('./src/components/SvgEditor/Editor/Dom/Node.vue')['default']
    NodeAttribute: typeof import('./src/components/SvgEditor/Panel/Right/Attribute/NodeAttribute/index.vue')['default']
    NodeMultiAttribute: typeof import('./src/components/SvgEditor/Panel/Right/Attribute/MultiAttribute/NodeMultiAttribute/index.vue')['default']
    NodeText: typeof import('./src/components/SvgEditor/Editor/Dom/NodeText.vue')['default']
    NPerformantEllipsis: typeof import('naive-ui')['NPerformantEllipsis']
    NRadio: typeof import('naive-ui')['NRadio']
    NRadioGroup: typeof import('naive-ui')['NRadioGroup']
    NScrollbar: typeof import('naive-ui')['NScrollbar']
    NSelect: typeof import('naive-ui')['NSelect']
    NSpace: typeof import('naive-ui')['NSpace']
    NSpin: typeof import('naive-ui')['NSpin']
    NSwitch: typeof import('naive-ui')['NSwitch']
    NTabPane: typeof import('naive-ui')['NTabPane']
    NTabs: typeof import('naive-ui')['NTabs']
    NTag: typeof import('naive-ui')['NTag']
    NTooltip: typeof import('naive-ui')['NTooltip']
    NTree: typeof import('naive-ui')['NTree']
    NUpload: typeof import('naive-ui')['NUpload']
    Panel: typeof import('./src/components/SvgEditor/Common/Panel/index.vue')['default']
    PanelScrollbar: typeof import('./src/components/SvgEditor/Common/PanelScrollbar/index.vue')['default']
    Rect: typeof import('./src/components/SvgEditor/Editor/Dom/Rect.vue')['default']
    RemoveMultiFromSublayer: typeof import('./src/components/SvgEditor/Modal/Sublayer/RemoveMultiFromSublayer.vue')['default']
    RemoveSelectedFromSublayer: typeof import('./src/components/SvgEditor/Modal/Sublayer/RemoveSelectedFromSublayer.vue')['default']
    Right: typeof import('./src/components/SvgEditor/Panel/Right/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Script: typeof import('./src/components/SvgEditor/Panel/Right/Script/index.vue')['default']
    Settings: typeof import('./src/components/SvgEditor/Header/Settings/index.vue')['default']
    Shortcut: typeof import('./src/components/SvgEditor/Shortcut/index.vue')['default']
    Sider: typeof import('./src/components/SvgEditor/Sider/index.vue')['default']
    StrokeDasharray: typeof import('./src/components/SvgEditor/Panel/Right/Attribute/Item/Link/StrokeDasharray.vue')['default']
    SublayerList: typeof import('./src/components/SvgEditor/Panel/Right/SublayerList/index.vue')['default']
    SvgIcon: typeof import('./src/components/MetaIcon/Modal/MetaIconData/SvgIcon.vue')['default']
    Text: typeof import('./src/components/SvgEditor/Editor/Dom/Text.vue')['default']
    TextAlign: typeof import('./src/components/SvgEditor/Panel/Right/Attribute/Item/Text/TextAlign.vue')['default']
    TextAttr: typeof import('./src/components/SvgEditor/Panel/Right/Attribute/NodeAttribute/TextAttr/index.vue')['default']
    TextMultiAttribute: typeof import('./src/components/SvgEditor/Panel/Right/Attribute/MultiAttribute/TextMultiAttribute/index.vue')['default']
    Trigger: typeof import('./src/components/SvgEditor/Panel/Right/Trigger/index.vue')['default']
    Update: typeof import('./src/components/SvgEditor/Modal/Sublayer/Update.vue')['default']
    Version: typeof import('./src/components/Version/index.vue')['default']
  }
}
