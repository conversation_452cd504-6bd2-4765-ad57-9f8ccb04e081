<template>
  <foreignObject :width="data.width" :height="data.height" :transform="textTransform">
    <div
      v-if="data.nodeText && data.nodeText !== '示例文字'"
      style="white-space: pre-wrap"
      :style="textStyle"
    >
      {{ data.nodeText }}
    </div>
  </foreignObject>
</template>

<script setup lang="ts">
import { computed } from "vue";

import type { INode } from "@/types";

const props = defineProps<{
  data: INode;
}>();

const textStyle = computed(() => {
  const { textStyle, style } = props.data;
  console.log("🚀 ~ textStyle ~ style:", style["justify-content"]);
  return {
    ...textStyle,
    display: (style.display || "block") as string,
    justifyContent: (style["justify-content"] || "unset") as string,
    alignItems: (style["align-items"] || "unset") as string,
    width: props.data.width + "px",
    height: props.data.height + "px",
    color: props.data.fontColor,
    fontSize: props.data.fontSize + "px"
  };
});

const textTransform = computed(() => {
  const textStyle = props.data.textStyle;
  return `translate(${textStyle.x || 0}, ${textStyle.y || 0})`;
});
</script>

<style></style>
