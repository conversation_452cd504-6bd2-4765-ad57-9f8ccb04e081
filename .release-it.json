{"hooks": {"after:bump": "echo 更新版本成功"}, "plugins": {"@release-it/conventional-changelog": {"preset": {"name": "conventionalcommits", "commitUrlFormat": "http://172.19.139.41:8088/nwang/topo-editor-base/-/commit/{{hash}}", "compareUrlFormat": "http://172.19.139.41:8088/nwang/topo-editor-base/-/compare/{{previousTag}}...{{currentTag}}", "types": [{"type": "feat", "section": "✨新功能"}, {"type": "fix", "section": "🐛Bug 修复"}, {"type": "chore", "hidden": true}, {"type": "docs", "hidden": true}, {"type": "style", "hidden": true}, {"type": "refactor", "hidden": true}, {"type": "perf", "hidden": true}, {"type": "test", "hidden": true}]}, "infile": "CHANGELOG.md", "ignoreRecommendedBump": true}}}