import { unref } from "vue";

import { useDataStore } from "@/stores";
import type { ILink, INode } from "@/types";
import { updateNodesLinks } from "@/utils/http/apis";
import { setLinkRelation, setNodeRelation } from "@/utils/tools";

interface IOperation {
  type: "ADD" | "DELETE" | "UPDATE";
  data: {
    nodes?: INode[];
    links?: ILink[];
  };
}

const recordNodes: INode[] = [];
const recordLinks: ILink[] = [];

const cutNode = (nodes?: INode[]) => {
  if (!nodes) return [];
  // 剪切节点
  return nodes.map((node) => {
    const item = {
      ...unref(node),
      targets: [],
      sources: []
    };

    return JSON.parse(JSON.stringify(item));
  });
};

const cutLinks = (links?: ILink[]) => {
  if (!links) return [];
  // 剪切线
  return links.map((link) => {
    const item = {
      ...unref(link),
      source: null,
      target: null
    };
    return JSON.parse(JSON.stringify(item));
  });
};

class OperationManager {
  private undoStack: IOperation[] = [];
  private redoStack: IOperation[] = [];

  constructor() {
    this.undoStack = [];
    this.redoStack = [];
  }

  /**
   * 预选
   * 鼠标点击时，记录当前选中的节点和线
   * @param data
   */
  preSelect(data: { nodes: INode[]; links: ILink[] }) {
    recordNodes.length = 0;
    recordLinks.length = 0;
    recordNodes.push(...cutNode(data.nodes));
    recordLinks.push(...cutLinks(data.links));
  }

  // 执行一个操作
  execute(
    data: {
      nodes?: INode[];
      links?: ILink[];
    },
    type: "ADD" | "DELETE" | "UPDATE"
  ) {
    const nodes = recordNodes.filter((node) =>
      data.nodes?.find((item) => item.nodeId === node.nodeId)
    );
    const links = recordLinks.filter((link) =>
      data.links?.find((item) => item.linkId === link.linkId)
    );
    console.log(recordNodes, nodes, data.nodes);

    const operation = {
      type,
      data: {
        nodes,
        links
      }
    };

    if (this.undoStack.length > 30) {
      this.undoStack.shift(); // 控制栈的最大长度
    }
    this.undoStack.push(operation);
    this.redoStack = []; // 新操作后，清空重做栈
    this.applyOperation(operation);
    console.log("执行", this.undoStack);
  }

  cancelExecute() {
    this.undoStack.pop();
    console.log("取消", this.undoStack);
  }

  // 应用操作（比如更新DOM，调用API等）
  applyOperation(operation: IOperation) {
    switch (operation.type) {
      case "ADD":
        this.addElement(operation);
        break;
      case "DELETE":
        this.deleteElement(operation);
        break;
      case "UPDATE":
        this.moveElement(operation);
        break;
      default:
        break;
    }
  }

  // 撤销操作
  undo() {
    const operation = this.undoStack.pop();
    if (operation) {
      this.redoStack.push(operation);
      this.undoAction(operation);
    }
  }

  // 重做操作
  redo() {
    const operation = this.redoStack.pop();
    if (operation) {
      this.undoStack.push(operation);
      this.applyOperation(operation);
    }
  }

  // 撤销操作的具体实现
  undoAction(operation: IOperation) {
    switch (operation.type) {
      case "ADD":
        this.deleteElement(operation);
        break;
      case "DELETE":
        this.addElement(operation);
        break;
      case "UPDATE":
        this.moveElementBack(operation);
        break;
      default:
        break;
    }
  }

  clearData() {
    this.undoStack = [];
    this.redoStack = [];
  }

  // TODO 新增操作
  // 各种具体的操作（例如新增、删除、移动等）
  addElement(operation: IOperation) {
    // 新增操作：恢复新增的元素
  }

  // TODO 删除操作
  async deleteElement(operation: IOperation) {
    // 删除操作：恢复删除的元素
    // await addNodeLinkList(
    //   {
    //     nodeList: operation.data.nodes || [],
    //     linkList: operation.data.links || []
    //   },
    //   true
    // );
  }

  moveElement(operation: IOperation) {
    // 移动操作：执行元素的移动
  }

  moveElementBack(operation: IOperation) {
    // 移动撤销操作：恢复元素的位置

    const dataStore = useDataStore();
    const nodes: INode[] = [];
    const links: ILink[] = [];

    dataStore.nodesAll.forEach((node) => {
      node.selected = false;
      const nodeRecord = operation.data.nodes?.find((item) => item.nodeId === node.nodeId);
      if (nodeRecord) {
        node = Object.assign(node, nodeRecord);
        setNodeRelation([node]);
        node.selected = true;
        nodes.push(node);
      }
    });

    dataStore.linksAll.forEach((link) => {
      link.selected = false;
      const linkRecord = operation.data.links?.find((item) => item.linkId === link.linkId);
      if (linkRecord) {
        link = Object.assign(link, linkRecord);
        setLinkRelation([link]);
        link.selected = true;
        links.push(link);
      }
    });

    console.log(
      "🚀 ~ OperationManager ~ dataStore.nodesAll.forEach ~ nodes:",
      nodes,
      dataStore.nodesSelected
    );

    updateNodesLinks(
      {
        nodes,
        links
      },
      true
    );
  }
}

export const operationManager = new OperationManager();
