import intersect from "path-intersection";
import { debounce } from "radash";

import { useCommonStore, useDataStore } from "@/stores";

const formatSelection2Path = (startPoints: number[], endPoints: number[]) => {
  const path = `M${startPoints[0]} ${startPoints[1]} H ${endPoints[0]} V ${endPoints[1]} H ${startPoints[0]} L ${startPoints[0]} ${startPoints[1]} Z`;
  return path;
};
/**
 * 通过selection获取nodes和links，根据选择框左上角和右下角的坐标，获取跟选择框有交集的link和node数据
 * @param startPoints
 * @param endPoints
 */
const selectNodesLinks = (startPoints: number[], endPoints: number[]) => {
  const dataStore = useDataStore();
  const commonStore = useCommonStore();

  dataStore.nodes.forEach((node) => {
    // 计算节点的边界框
    const nodeRect = {
      left: node.x,
      right: node.x + node.width,
      top: node.y,
      bottom: node.y + node.height
    };

    // 计算选择框的边界框
    const selectionRect = {
      left: Math.min(startPoints[0], endPoints[0]),
      right: Math.max(startPoints[0], endPoints[0]),
      top: Math.min(startPoints[1], endPoints[1]),
      bottom: Math.max(startPoints[1], endPoints[1])
    };

    // 判断两个边界框是否相交
    if (
      nodeRect.left < selectionRect.right &&
      nodeRect.right > selectionRect.left &&
      nodeRect.top < selectionRect.bottom &&
      nodeRect.bottom > selectionRect.top
    ) {
      node.selected = true;
      dataStore.currentNode = node;
    } else {
      if (commonStore.isShiftDown) return;
      node.selected = false;
    }
  });

  dataStore.links.forEach((link) => {
    const path = formatSelection2Path(startPoints, endPoints);
    const intersection = intersect(path, link.linkPath);

    const allPointsInSelection = link.points.every((point) => {
      let isXInRect = true;
      let isYInRect = true;

      if ("x" in point) {
        isXInRect =
          point.x >= Math.min(startPoints[0], endPoints[0]) &&
          point.x <= Math.max(startPoints[0], endPoints[0]);
      }
      if ("y" in point) {
        isYInRect =
          point.y >= Math.min(startPoints[1], endPoints[1]) &&
          point.y <= Math.max(startPoints[1], endPoints[1]);
      }
      return isXInRect && isYInRect;
    });

    if (intersection.length > 0 || allPointsInSelection) {
      link.selected = true;
      dataStore.currentLink = link;
    } else {
      if (commonStore.isShiftDown) return;
      link.selected = false;
    }
  });
};

export const selectNodesLinksByDebounce = debounce({ delay: 50 }, selectNodesLinks);
