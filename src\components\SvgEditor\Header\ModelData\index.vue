<template>
  <n-button quaternary size="small" class="mr-1" @click="showModelDataModal">
    <n-icon size="20"><DatabaseSearch24Regular /></n-icon>
  </n-button>

  <ModelDataModal ref="modelDataModalRef" />
</template>

<script setup lang="ts">
import { DatabaseSearch24Regular } from "@/utils/components/icons";

import ModelDataModal from "@/components/SvgEditor/Modal/Header/ModelData/index.vue";
import { useTemplateRef } from "vue";

const modelDataModalRef = useTemplateRef("modelDataModalRef");

const showModelDataModal = () => {
  modelDataModalRef.value?.show();
};
</script>
