import { debounce } from "radash";

import type {
  ILink,
  INode,
  INodeLinkSource,
  IOriginalLink,
  IOriginalNode,
  ISourceLink,
  ISourceNode
} from "@/types";
import { operationManager } from "@/utils/editor/data/operationManager";
import { formatLinkParams, formatNodeParams } from "@/utils/tools";

import request from "../../index";

interface IDeleteNodeModel {
  nodeIdList: string[];
  mapId: string;
}

interface IDeleteLinkModel {
  linkIdList: string[];
  mapId: string;
}

export const getNodeLinkListByMapId = (mapId: string) => {
  return request.get<INodeLinkSource>({
    url: "/topoEdit/getNodeLinkListByMapId",
    params: { mapId }
  });
};

export const addNodeLinkList = (params: {
  nodeList: IOriginalNode[] | INode[];
  linkList: IOriginalLink[] | INode[];
}) => {
  const p = request.post<{
    nodeList: ISourceNode[];
    linkList: ISourceLink[];
  }>({ url: "/topoEdit/insertNodeLinkList", data: params });

  // if (!isBackRecord) {
  //   operationManager.execute(
  //     {
  //       nodes: params.nodeList as INode[],
  //       links: params.linkList as ILink[]
  //     },
  //     "ADD"
  //   );
  // }

  return p;
};

export const addNode = (params: IOriginalNode | INode, isBackRecord?: boolean) => {
  const p = request.post<string>({ url: "/topoEdit/insertNode", data: params });

  // if (!isBackRecord) {
  //   operationManager.execute(
  //     {
  //       nodes: [params as INode]
  //     },
  //     "ADD"
  //   );
  // }
  return p;
};

export const updateNodeByHttp = async (
  nodes: INode[],
  isBackRecord?: boolean,
  isMultip?: boolean
) => {
  if (!nodes.length) return Promise.resolve();
  const nodeList = formatNodeParams(nodes);

  // 如果是批量更新，不需要存入操作栈
  if (!isMultip) {
    operationManager.execute(
      {
        nodes
      },
      "UPDATE"
    );
  }

  const p = await request.post<string>({ url: "/topoEdit/updateNode", data: { nodeList } });
  if (!isBackRecord && !isMultip) {
    // 如果不是撤销操作和批量更新，提示更新成功
    window.$message.success("更新成功");
  }
  return p;
};

export const updateNode = debounce({ delay: 250 }, updateNodeByHttp);

export const deleteNodes = (params: IDeleteNodeModel) => {
  return request.post({ url: "/topoEdit/deleteNode", data: params });
};

export const addLink = (params: IOriginalLink | ILink) => {
  const p = request.post<string>({ url: "/topoEdit/insertLink", data: params });

  // if (!isBackRecord) {
  //   operationManager.execute(
  //     {
  //       links: [params as ILink]
  //     },
  //     "ADD"
  //   );
  // }

  return p;
};

export const updateLinkByHttp = async (
  links: ILink[],
  isBackRecord?: boolean,
  isMultip?: boolean
) => {
  if (!links.length) return Promise.resolve();
  const linkList = formatLinkParams(links);

  // 如果是批量更新，不需要存入操作栈
  if (!isMultip) {
    operationManager.execute(
      {
        links
      },
      "UPDATE"
    );
  }

  const p = await request.post<string>({ url: "/topoEdit/updateLink", data: { linkList } });
  if (!isBackRecord && !isMultip) {
    // 如果不是撤销操作和批量更新，提示更新成功
    window.$message.success("更新成功");
  }
  return p;
};

export const updateLink = debounce({ delay: 250 }, updateLinkByHttp);

export const deleteLinks = (params: IDeleteLinkModel) => {
  // const dataStore = useDataStore();

  // operationManager.execute(
  //   {
  //     links: dataStore
  //   },
  //   "UPDATE"
  // );
  return request.post({ url: "/topoEdit/deleteLink", data: params });
};

export const updateNodes = (params: IOriginalNode[]) => {
  return request.post({
    url: "/topoEdit/updateNode",
    data: {
      nodeList: params
    }
  });
};

export const updateLinks = (params: ILink[]) => {
  return request.post({
    url: "/topoEdit/updateLink",
    data: {
      linkList: params
    }
  });
};

/**
 * isBackRecord  是否是撤销操作
 * @param params
 * @param isBackRecord
 */
const updateNodesLinksByHttp = async (
  params: { nodes: INode[]; links: ILink[] },
  isBackRecord?: boolean
) => {
  const nodeList = formatNodeParams(params.nodes);
  const linkList = formatLinkParams(params.links);

  if (!isBackRecord) {
    operationManager.execute(
      {
        nodes: nodeList,
        links: linkList
      },
      "UPDATE"
    );
  }

  const p1 = nodeList.length && updateNodeByHttp(nodeList, isBackRecord, true);
  const p2 = linkList.length && updateLinkByHttp(linkList, isBackRecord, true);

  await Promise.all([p1, p2]);
  if (!isBackRecord) {
    window.$message.success("更新成功");
  }
};

export const updateNodesLinks = debounce({ delay: 250 }, updateNodesLinksByHttp);
