<template>
  <PanelScrollbar>
    <n-checkbox-group :value="mapStore.sublayerIds" @update:value="handleUpdateValue">
      <div v-for="(item, index) in sublayerList" :key="index" class="flex flex-col">
        <n-divider
          v-if="item.title !== '未分配'"
          title-placement="left"
          class="text-xs text-gray-400"
          >{{ item.title }}</n-divider
        >
        <div
          v-for="itemChild in item.list"
          :key="itemChild.sublayerId"
          class="flex justify-between mb-1"
        >
          <n-checkbox :value="itemChild.sublayerId" :label="itemChild.sublayerName" />
          <div v-if="itemChild.sublayerId !== 'other'" class="pr-2">
            <n-tooltip trigger="hover">
              <template #trigger>
                <n-button
                  text
                  style="font-size: 20px"
                  class="mx-2"
                  @click="handleCheckInfo(itemChild)"
                >
                  <n-icon>
                    <Edit />
                  </n-icon>
                </n-button>
              </template>
              编辑
            </n-tooltip>
            <n-tooltip trigger="hover">
              <template #trigger>
                <n-button
                  text
                  type="error"
                  style="font-size: 20px"
                  class="mx-1"
                  @click="handleDelete(itemChild)"
                >
                  <n-icon>
                    <Delete />
                  </n-icon>
                </n-button>
              </template>
              移除子节点内的元素
            </n-tooltip>
          </div>
        </div>
      </div>
    </n-checkbox-group>
  </PanelScrollbar>
  <SublayerModal ref="sublayerModalRef" />
  <RemoveMultiFromSublayer ref="RemoveMultiFromSublayerRef" />
</template>

<script setup lang="ts">
import { computed, ref } from "vue";

import PanelScrollbar from "@/components/SvgEditor/Common/PanelScrollbar/index.vue";
import RemoveMultiFromSublayer from "@/components/SvgEditor/Modal/Sublayer/RemoveMultiFromSublayer.vue";
import SublayerModal from "@/components/SvgEditor/Modal/Sublayer/Update.vue";
import BaseGrid from "@/components/Common/BaseGrid/index.vue";

import { useMapSublayer } from "@/hooks/map/useMapSublayer";
import { useMapStore } from "@/stores";
import type { ISublayer } from "@/types";
import { Delete, Edit } from "@/utils/components/icons";
import { renewNodesLinks } from "@/utils/tools";

const mapStore = useMapStore();
useMapSublayer();

const sublayerModalRef = ref<InstanceType<typeof SublayerModal> | null>(null);
const RemoveMultiFromSublayerRef = ref<InstanceType<typeof RemoveMultiFromSublayer> | null>(null);

const sublayerList = computed(() => {
  return [
    {
      title: "未分配",
      list: mapStore.sublayers.filter((item) => item.sublayerId === "other")
    },
    {
      title: "默认",
      list: mapStore.sublayers.filter((item) => item.sublayerId !== "other" && item.isVisible === 1)
    },
    {
      title: "自定义",
      list: mapStore.sublayers.filter((item) => item.sublayerId !== "other" && item.isVisible === 0)
    }
  ].filter((item) => item.list.length);
});

// 根据子图层， 显示页面中的元素
const handleUpdateValue = (val: string[]) => {
  mapStore.sublayerIds = mapStore.sublayers
    .filter((sublayer) => val.includes(sublayer.sublayerId))
    .map((sublayer) => sublayer.sublayerId);
  renewNodesLinks();
  //   drawNodesLinks();
};

const handleCheckInfo = (sublayer: ISublayer) => {
  sublayerModalRef.value?.show(sublayer);
};

// 删除元素含有当前的图层
const handleDelete = (sublayer: ISublayer) => {
  RemoveMultiFromSublayerRef.value?.show(sublayer);
};
</script>

<style>
.n-checkbox__label {
  word-break: break-all;
}
</style>
