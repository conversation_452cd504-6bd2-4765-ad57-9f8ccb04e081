<template>
  <n-modal
    v-model:show="isVisible"
    preset="dialog"
    title="编辑"
    size="huge"
    :bordered="false"
    :show-icon="false"
    :close-on-esc="false"
    :maskClosable="false"
    positive-text="确认"
    negative-text="取消"
    @positive-click="submit"
    @negative-click="hide"
    style="margin-top: 20vh; width: 600px"
  >
    <n-grid :cols="24" x-gap="12" class="mt-2">
      <n-gi :span="10"> Key </n-gi>
      <n-gi :span="10"> Value </n-gi>
    </n-grid>
    <n-scrollbar style="max-height: 300px">
      <n-grid v-for="(data, index) in metaData" :key="index" x-gap="12" :cols="24" class="mt-2">
        <n-gi :span="6">
          <n-input v-model:value="data.key" size="small" />
        </n-gi>
        <n-gi :span="16">
          <n-input v-model:value="data.value" size="small" />
        </n-gi>
        <n-gi :span="2">
          <div class="h-full flex items-center">
            <n-button text class="mx-1" @click="addMetaData(index)">
              <n-icon :component="Add" />
            </n-button>

            <n-button
              text
              type="error"
              class="mx-1"
              @click="deleteMetaData(index)"
              v-if="metaData.length > 1"
            >
              <n-icon :component="Subtract" />
            </n-button>
          </div>
        </n-gi>
      </n-grid>
    </n-scrollbar>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, toRaw } from "vue";

import { Add, Subtract } from "@/utils/components/icons";

const emit = defineEmits<{
  "update:value": [data: Record<string, any>];
}>();

const isVisible = ref(false);

const metaData = ref<Record<string, any>[]>([]);

// 数据转换 { "use": "医药中间体", "name": "邻甲氧基苯甲酰氯 331", "graphId": "5138" }
// => [ { key: "use", value: "医药中间体" }, { key: "name", value: "邻甲氧基苯甲酰氯 331" }, { key: "graphId", value: "5138" } ]
const formatObject = (data: Record<string, any>) => {
  return Object.keys(data).map((key) => {
    return {
      key,
      value: data[key]
    };
  });
};

const addMetaData = (index: number) => {
  metaData.value.splice(index + 1, 0, { key: "", value: "" });
};

const deleteMetaData = (index: number) => {
  metaData.value.splice(index, 1);
};
const show = (data: Record<string, any>) => {
  console.log("🚀 ~ show ~ data:", data);
  isVisible.value = true;
  const d = formatObject(toRaw(data));
  metaData.value = d.length ? d : [{ key: "", value: "" }];
};

const hide = () => {
  isVisible.value = false;
};

const submit = () => {
  emit("update:value", metaData.value);
  hide();
  return false;
};

defineExpose({
  show
});
</script>
