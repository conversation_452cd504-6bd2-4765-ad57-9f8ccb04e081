import { globalIgnores } from "eslint/config";
import { defineConfigWithVueTs, vueTsConfigs } from "@vue/eslint-config-typescript";
import pluginVue from "eslint-plugin-vue";
import pluginOxlint from "eslint-plugin-oxlint";
// import simpleImportSort from "eslint-plugin-simple-import-sort";
// import unusedImports from "eslint-plugin-unused-imports";
import skipFormatting from "@vue/eslint-config-prettier/skip-formatting";

export default defineConfigWithVueTs(
  {
    name: "app/files-to-lint",
    files: ["**/*.{ts,mts,tsx,vue}"]
  },
  globalIgnores(["**/dist/**", "**/dist-ssr/**", "**/coverage/**"]),

  pluginVue.configs["flat/essential"],
  vueTsConfigs.recommended,
  ...pluginOxlint.configs["flat/recommended"],
  skipFormatting,
  {
    plugins: {
      // "unused-imports": unusedImports,
      // "simple-import-sort": simpleImportSort
    },
    rules: {
      "vue/multi-word-component-names": "off",
      "@typescript-eslint/no-explicit-any": "off",
      "unused-imports/no-unused-imports": "error",
      "unused-imports/no-unused-vars": [
        "warn",
        {
          vars: "all",
          varsIgnorePattern: "^_",
          args: "after-used",
          argsIgnorePattern: "^_"
        }
      ],
      // "simple-import-sort/imports": [
      //   "error",
      //   {
      //     groups: [
      //       // vue放在首行
      //       ["^vue", "^@?\\w"],
      //       // Internal packages.
      //       ["^(@|components)(/.*|$)"],
      //       // Side effect imports.
      //       ["^\\u0000"],
      //       // Parent imports. Put `..` last.
      //       ["^\\.\\.(?!/?$)", "^\\.\\./?$"],
      //       // Other relative imports. Put same-folder imports and `.` last.
      //       ["^\\./(?=.*/)(?!/?$)", "^\\.(?!/?$)", "^\\./?$"],
      //       // Style imports.
      //       ["^.+\\.?(css)$"]
      //     ]
      //   }
      // ],
      // "simple-import-sort/imports": "error",
      // "simple-import-sort/exports": "error"
    }
  }
);
