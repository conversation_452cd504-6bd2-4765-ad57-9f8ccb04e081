import * as d3 from "d3";
export const useExportSvg = () => {
  /**
   * 导出svg
   * @param filename 文件名
   */
  const exportSvg = async (filename: string, width: number, height: number) => {
    const svg = d3.select("#svgEditor").clone(true);
    svg
      .attr("viewBox", `0 0 ${width} ${height}`)
      .style(
        "font-family",
        `v-sans, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"`
      )
      .style("font-size", "14px");

    svg.select("#map").attr("transform", "scale(1)");
    svg.select("#mapBackground").attr("fill", "#fff");
    svg.selectAll(".node-text-rect").attr("stroke", "#ccc").attr("rx", "2");
    const svgElement = svg.node() as Element;

    if (svgElement) {
      await changeBackgroundImage(svgElement);
      const svgData = svgElement ? new XMLSerializer().serializeToString(svgElement as Node) : "";
      const svgBlob = new Blob([svgData], { type: "image/svg+xml;charset=utf-8" });
      const url = URL.createObjectURL(svgBlob);
      const downloadLink = document.createElement("a");
      downloadLink.href = url;
      downloadLink.download = filename + ".svg";
      document.body.appendChild(downloadLink);
      downloadLink.click();
      downloadLink.remove();
      svg.remove();
    }
  };

  /**
   * 将SVG节点转换为指定类型的图像。
   *
   * @param name 生成图像的文件名。
   * @param width 生成图像的宽度。
   * @param height 生成图像的高度。
   * @param type 生成图像的类型，默认为"png"。
   */
  const covertSVG2Image = async (name: any, width: number, height: number, type = "png") => {
    const svg = d3.select("#svgEditor").clone(true);
    svg
      .attr("viewBox", `0 0 ${width} ${height}`)
      .style(
        "font-family",
        `v-sans, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"`
      )
      .style("font-size", "14px");

    svg.select("#map").attr("transform", "scale(1)");
    svg.select("#mapBackground").attr("fill", "#fff");
    svg.selectAll(".node-text-rect").attr("stroke", "#ccc").attr("rx", "2");
    const svgElement = svg.node() as Element;
    if (svgElement) {
      await changeBackgroundImage(svgElement);
      const serializer = new XMLSerializer();
      const source =
        '<?xml version="1.0" standalone="no"?>\r\n' +
        serializer.serializeToString(svgElement as Node);
      const image = new Image();
      const src = "data:image/svg+xml;charset=utf-8," + encodeURIComponent(source);
      image.src = src;
      const canvas = document.createElement("canvas");
      canvas.width = width;
      canvas.height = height;
      svg.remove();
      const context = canvas.getContext("2d");
      if (context !== null && context !== undefined) {
        context.scale(1, 1);
        image.onload = function () {
          context.drawImage(image, 0, 0);
          const a = document.createElement("a");
          a.download = `${name}.${type}`;
          a.href = canvas.toDataURL(`image/${type}`);
          a.click();
        };
      }
    }
  };

  async function changeBackgroundImage(svgElement: Element) {
    if (svgElement) {
      const divElements = svgElement.querySelectorAll("div");
      for (const div of Array.from(divElements)) {
        const imageUrl = div.getAttribute("data-url");
        if (imageUrl) {
          try {
            const newBackgroundUrl = await convertImgToBase64(imageUrl);
            div.style.backgroundImage = `url(${newBackgroundUrl})`;
          } catch (error) {
            console.error("Error converting image to base64:", error);
          }
        }
      }
    }
  }

  function convertImgToBase64(url: string) {
    return new Promise((resolve, reject) => {
      let canvas: HTMLCanvasElement | null = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      const img = new Image(); // 服务端也要设置允许跨域
      img.crossOrigin = "Anonymous";
      img.src = url;
      img.onload = function () {
        canvas!.height = img.height;
        canvas!.width = img.width;
        ctx?.drawImage(img, 0, 0);
        const dataURL = canvas!.toDataURL("image/png");
        canvas = null;
        resolve(dataURL);
      };
      img.onerror = function () {
        reject("");
      };
    });
  }

  return {
    exportSvg,
    covertSVG2Image
  };
};
