import * as d3 from "d3";

import { useCommonStore } from "@/stores";
import { operationManager } from "@/utils/editor/data/operationManager";
import emitter from "@/utils/mitt";
import { cloneNodesLinks, moveNodeLinkList, pasteData } from "@/utils/tools";

import { hideSelectionRect } from "../../draw";

const handleArrowKey = (e: KeyboardEvent) => {
  const step = e.shiftKey ? 5 : 1;
  let x = 0;
  let y = 0;
  switch (e.code) {
    case "ArrowUp":
      y -= step;
      break;
    case "ArrowDown":
      y += step;
      break;
    case "ArrowLeft":
      x -= step;
      break;
    case "ArrowRight":
      x += step;
      break;
  }

  console.log(x, y);
  moveNodeLinkList(x, y);
};

export const bindWindowEvent = () => {
  const commonStore = useCommonStore();

  // 是否按住空格
  d3.select("#svgEditor").on("keydown", function (e) {
    switch (e.code) {
      case "ShiftLeft":
        commonStore.isShiftDown = true;
        break;
      case "Space":
        commonStore.isSpaceDown = true;
        hideSelectionRect();
        break;
      case "Delete":
        emitter.emit("on:delete");
        break;
      // ctrl + a
      case "KeyA":
        if (e.ctrlKey) {
          e.preventDefault();
          commonStore.isCtrlADown = true;
        }
        break;
      // ctrl + c
      case "KeyC":
        if (e.ctrlKey) {
          e.preventDefault();
          cloneNodesLinks();
        }
        break;
      // ctrl + v
      case "KeyV":
        if (e.ctrlKey) {
          e.preventDefault();
          pasteData();
        }
      // ctrl + z
      case "KeyZ":
        if (e.ctrlKey) {
          e.preventDefault();
          operationManager.undo();
        }
        break;
      case "ArrowUp":
      case "ArrowDown":
      case "ArrowLeft":
      case "ArrowRight":
        e.preventDefault();
        handleArrowKey(e);
        break;
    }
  });
  d3.select("body").on("keyup", function (e) {
    switch (e.code) {
      case "ShiftLeft":
        commonStore.isShiftDown = false;
        break;
      case "Space":
        commonStore.isSpaceDown = false;
        break;
      case "KeyA":
        commonStore.isCtrlADown = false;
        break;
    }
  });
};

export const unbindWindowEvent = () => {
  d3.select("#svgEditor").on("keydown", null);
  d3.select("#svgEditor").on("keyup", null);
};
