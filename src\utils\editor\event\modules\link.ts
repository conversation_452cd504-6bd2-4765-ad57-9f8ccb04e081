import * as d3 from "d3";
import { SVGPathData } from "svg-pathdata";

import { useCommonStore, useDataStore, useMapStore, useSvgStore } from "@/stores";
import type { ILink, ISVGG } from "@/types";
import { updateLink } from "@/utils/http/apis";
import { setLinkSelected } from "@/utils/tools";

import { operationManager } from "../../data/operationManager";

const startPoint = {
  x: 0,
  y: 0
};

let tx = 0;
let ty = 0;
const dragStart = (e: any, d: ILink) => {
  console.log("🚀 ~ dragStart ~ d:", d);
  const commonStore = useCommonStore();
  const svgStore = useSvgStore();

  if (commonStore.isSpaceDown) return;
  setLinkSelected(d);

  operationManager.preSelect({
    nodes: [],
    links: [d]
  });

  if (!svgStore.editable) return;

  startPoint.x = e.x;
  startPoint.y = e.y;
};

const dragging = (e: any, d: ILink) => {
  const commonStore = useCommonStore();
  const svgStore = useSvgStore();
  if (!svgStore.editable || commonStore.isSpaceDown) return;
  tx = e.x - startPoint.x;
  ty = e.y - startPoint.y;
  d.transform.x = tx;
  d.transform.y = ty;

  //   attrLinkGTrans(el, tx, ty);
};

const dragEnd = (e: any, d: ILink) => {
  const commonStore = useCommonStore();
  const svgStore = useSvgStore();

  //   attrSelectionDrag(false);

  //   表示连线没有移动
  if (tx === 0 && ty === 0) return;
  if (!svgStore.editable || commonStore.isSpaceDown) return;

  const d1 = new SVGPathData(d.linkPath);
  d.linkPath = d1.translate(d.transform.x, d.transform.y).toAbs().encode();

  d.x += d.transform.x;
  d.y += d.transform.y;
  startPoint.x = 0;
  startPoint.y = 0;
  tx = 0;
  ty = 0;

  d.transform.x = 0;
  d.transform.y = 0;

  //   更新接口
  updateLink([d]);
};

export const bindLinkDrag = (linkG: ISVGG<ILink, SVGGElement | HTMLElement>) => {
  const mapStore = useMapStore();
  const dataStore = useDataStore();

  const drag = d3
    .drag<SVGGElement, ILink>()
    .on("start", function (e, d) {
      dragStart(e, d);
    })
    .on("drag", function (e, d) {
      dragging(e, d);
    })
    .on("end", function (e, d) {
      dragEnd(e, d);
    });

  linkG.call(drag);

  linkG
    .on("mouseenter", (e, d) => {
      dataStore.previewLink = d;
    })
    .on("mouseleave", () => {
      dataStore.previewLink = null;
    })
    .on("mousedown", dragStart)
    .on("contextmenu", (e, d) => {
      e.preventDefault();
      e.stopPropagation();
      mapStore.showMapMenu({ x: e.clientX, y: e.clientY }, "link");
    });
};
