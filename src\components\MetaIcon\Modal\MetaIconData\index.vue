<template>
  <n-modal
    v-model:show="isVisible"
    preset="dialog"
    title="图元数据配置"
    :bordered="false"
    :show-icon="false"
    :close-on-esc="false"
    :maskClosable="false"
    style="margin-top: 15vh; width: 800px"
  >
    <!-- <SvgIcon v-if="!isLink" ref="svgIconRef" @onIdSelect="onIdSelect"></SvgIcon> -->
    <CondintionList ref="condintionListRef"></CondintionList>
    <template #footer> 尾部 </template>
  </n-modal>
</template>

<script setup lang="ts">
import { nextTick, ref } from "vue";

import { useDataStore } from "@/stores";
import type { IMetaItem } from "@/types";
import type { ITreeOption } from "@/utils/tools/data/modules/bind";

import CondintionList from "./CondintionList.vue";
import SvgIcon from "./SvgIcon.vue";

const dataStore = useDataStore();

const isLink = ref(false);
const isVisible = ref(false);
const svgIconRef = ref<InstanceType<typeof SvgIcon> | null>(null);
const condintionListRef = ref<InstanceType<typeof CondintionList> | null>(null);

const onIdSelect = (val?: ITreeOption) => {
  condintionListRef.value?.onIdChange(val);
};

const iconMetaInfo = ref<IMetaItem>();

const show = async (val: IMetaItem) => {
  isVisible.value = true;
  isLink.value = val.compClass === "link";
  nextTick(() => {
    svgIconRef.value?.initData(val);
    condintionListRef.value?.initData(val);
  });
};

const hide = () => {
  isVisible.value = false;
};

const submit = () => {
  console.log("data", dataStore.currentNode, iconMetaInfo.value);
};

defineExpose({
  show
});
</script>

<style scoped></style>
