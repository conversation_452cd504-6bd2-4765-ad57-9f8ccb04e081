<template>
  <n-scrollbar style="height: calc(100vh - 110px)">
    <n-collapse>
      <n-collapse-item title="连线">
        <div class="flex flex-col">
          <n-checkbox v-model:checked="svgStore.isDrawLink"> 开启画线 </n-checkbox>
        </div>
      </n-collapse-item>
      <n-collapse-item
        v-for="(item, index) in metaStore.metaList"
        :key="index"
        :title="item.groupName"
        :name="index"
      >
        <n-grid :x-gap="12" :y-gap="8" :cols="3">
          <n-grid-item
            v-for="(itemChild, index) in item.objList"
            :key="index"
            draggable="true"
            @dragstart="handleDragStart($event, itemChild)"
            class="flex flex-col items-center px-4 py-2 bg-#3b3b3b rounded-1 hover:opacity-80 cursor-grab"
          >
            <img
              :src="item.groupId === 'base' ? itemChild.objImg : urlPrefix + itemChild.objImg"
              class="w-6 h-6 select-none"
              draggable="false"
            />
            <span class="text-xs mt-2 select-none break-all">{{ itemChild.objName }}</span>
          </n-grid-item>
        </n-grid>
      </n-collapse-item>
    </n-collapse>
  </n-scrollbar>
</template>

<script setup lang="ts">
import { onBeforeUnmount, onMounted, watch } from "vue";

import { useMapStore, useMetaStore, useSvgStore } from "@/stores";
import type { IMetaItem } from "@/types";
import { clearLinkDraft } from "@/utils/editor/draw";
import { getImageUrl } from "@/utils/tools";

const metaStore = useMetaStore();
const svgStore = useSvgStore();
const mapStore = useMapStore();

const urlPrefix = getImageUrl();
const handleDragStart = (e: DragEvent, val: IMetaItem) => {
  e.dataTransfer?.setData("text/plain", JSON.stringify(val));
};

onMounted(() => {
  metaStore.getMetaList();
});

watch(
  () => mapStore.mapInfo,
  () => {
    clearLinkDraft();
  }
);

watch(
  () => svgStore.isDrawLink,
  (val) => {
    !val && clearLinkDraft();
  }
);

onBeforeUnmount(() => {
  clearLinkDraft();
});
</script>

<style></style>
