import { toRaw } from "vue";

import type { IGroupData, IMapSource, ISourceLink, ISourceNode, ISublayer } from "@/types";
import { getMapGroupData, getNodeLinkListByMapId, getSublayers } from "@/utils/http/apis";

export const useExportJson = () => {
  const exportJson = async (mapInfo: IMapSource) => {
    const { mapId, mapName } = mapInfo;
    const sublayers = await getSublayers(mapId);
    const sublayers_json = toRaw(sublayers);
    const mapContent = await getNodeLinkListByMapId(mapId);
    const { nodes, links } = mapContent;
    const mapContent_json = {
      nodes: toRaw(nodes),
      links: toRaw(links)
    };

    const groupData = await getMapGroupData(mapId);

    const exportData: {
      mapInfo: IMapSource;
      mapContent: {
        nodes: ISourceNode[];
        links: ISourceLink[];
      };
      sublayers: ISublayer[];
      hotspots: [];
      groupData: IGroupData[];
    } = {
      mapInfo,
      mapContent: mapContent_json,
      sublayers: sublayers_json,
      hotspots: [],
      groupData
    };

    downloadJSON(exportData, mapName);
  };
  const downloadJSON = (data: any, filename = "svg") => {
    // 确保文件名不包含.json后缀
    filename = filename.endsWith(".json") ? filename.slice(0, -5) : filename;
    // 将对象转换为格式化的JSON字符串
    const jsonString = typeof data === "object" ? JSON.stringify(data, null, 2) : data;
    // 创建Blob对象
    const blob = new Blob([jsonString], { type: "application/json" });
    // 创建下载链接
    const a = document.createElement("a");
    a.download = `${filename}.json`;
    a.href = URL.createObjectURL(blob);
    // 模拟点击下载
    document.body.appendChild(a);
    a.click();
    // 清理
    document.body.removeChild(a);
    URL.revokeObjectURL(a.href);
  };

  return {
    exportJson
  };
};
