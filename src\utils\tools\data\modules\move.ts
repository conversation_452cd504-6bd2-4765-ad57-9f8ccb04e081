import { useDataStore } from "@/stores";
import { updateNodesLinks } from "@/utils/http/apis";
import { SVGPathData } from "svg-pathdata";

export const moveNodeLinkList = (x: number, y: number) => {
  const dataStore = useDataStore();

  dataStore.nodesSelected.forEach((node) => {
    node.x += x;
    node.y += y;
    node.nodePosition = `${node.x},${node.y}`;
  });

  dataStore.linksSelected.forEach((link) => {
    const pathData = new SVGPathData(link.linkPath);
    link.linkPath = pathData.translate(x, y).toAbs().encode();
    link.points = new SVGPathData(link.linkPath).commands;
  });

  updateNodesLinks({
    nodes: dataStore.nodesSelected,
    links: dataStore.linksSelected
  });
};
