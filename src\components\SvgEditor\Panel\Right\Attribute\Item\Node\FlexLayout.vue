<template>
  <BaseGrid title="布局">
    <div class="flex flex-1">
      <n-button
        text
        style="font-size: 20px"
        class="mr-3"
        @click="setTextLayout('justify-content', 'left')"
      >
        <n-icon>
          <FormatAlignLeftFilled />
        </n-icon>
      </n-button>
      <n-button
        text
        style="font-size: 20px"
        class="mr-3"
        @click="setTextLayout('justify-content', 'center')"
      >
        <n-icon>
          <FormatAlignCenterFilled />
        </n-icon>
      </n-button>
      <n-button
        text
        style="font-size: 20px"
        class="mr-3"
        @click="setTextLayout('justify-content', 'end')"
      >
        <n-icon>
          <FormatAlignRightFilled />
        </n-icon>
      </n-button>
    </div>

    <div class="flex flex-1">
      <!-- <n-button
        text
        style="font-size: 20px"
        class="mr-3 rotate-90"
        @click="setTextLayout('align-items', 'left')"
      >
        <n-icon>
          <FormatAlignLeftFilled />
        </n-icon>
      </n-button> -->
      <n-button
        text
        style="font-size: 20px"
        class="mr-3 rotate-90"
        @click="setTextLayout('align-items', 'center')"
      >
        <n-icon>
          <FormatAlignCenterFilled />
        </n-icon>
      </n-button>
      <!-- <n-button
        text
        style="font-size: 20px"
        class="mr-3 rotate-90"
        @click="setTextLayout('align-items', 'end')"
      >
        <n-icon>
          <FormatAlignRightFilled />
        </n-icon>
      </n-button> -->
    </div>
  </BaseGrid>
</template>

<script setup lang="ts">
import type { INodeAlign } from "@/types";
import {
  FormatAlignCenterFilled,
  FormatAlignLeftFilled,
  FormatAlignRightFilled
} from "@/utils/components/icons";

import BaseGrid from "@/components/Common/BaseGrid/index.vue";

const emit = defineEmits<{
  "update:value": [direction: string, value: INodeAlign];
}>();

defineProps<{
  value?: string | number | null;
}>();

const setTextLayout = (direction: string, value: INodeAlign) => {
  emit("update:value", direction, value);
};
</script>

<style></style>
