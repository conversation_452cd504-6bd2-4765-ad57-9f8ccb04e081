<template>
  <image :width="data.width" :height="data.height" :style="nodeStyles" :href="imageUrl" />
  <Text :data="data" v-if="data.nodeText" />
  <Divider :data="data" :node-styles="nodeStyles" />
</template>

<script setup lang="ts">
import { computed } from "vue";
import type { INode, IStyle } from "@/types";

import Text from "./Text.vue";
import Divider from "./Divider.vue";

import DefaultImage from "@/assets/images/meta/Image.svg?url";

// 常量定义
const HIGH_VOLT_LEVELS = ["_500KV", "_1000KV", "_1750KV", "_3000KV"];
const MEDIUM_VOLT_LEVELS = ["_220KV", "_110KV"];

const props = defineProps<{
  data: INode;
}>();

// 类型定义
interface MetaData {
  powerType?: string;
  powerTypeByCustom?: string;
  volt?: string;
  type?: string;
  xfmrNo?: string;
  stationType?: string;
  switchNo?: string;
  switchRunningNo?: string;
  [key: string]: any;
}

interface NodeStylesResult extends IStyle {
  filter?: string;
}

// 过滤器颜色列表 - 用于SVG滤镜效果
const filterColorList: Record<string, string> = {
  green:
    "brightness(0) saturate(100%) invert(23%) sepia(99%) saturate(1439%) hue-rotate(100deg) brightness(107%) contrast(106%)",
  pink: "brightness(0) saturate(100%) invert(29%) sepia(37%) saturate(5527%) hue-rotate(310deg) brightness(100%) contrast(102%)",
  black:
    "brightness(0) saturate(100%) invert(0%) sepia(100%) saturate(0%) hue-rotate(234deg) brightness(96%) contrast(107%)",
  red: "brightness(0) saturate(100%) invert(10%) sepia(81%) saturate(6148%) hue-rotate(347deg) brightness(92%) contrast(98%)",
  orange:
    "brightness(0) saturate(100%) invert(32%) sepia(79%) saturate(3277%) hue-rotate(1deg) brightness(102%) contrast(104%)",
  blue: "brightness(0) saturate(100%) invert(36%) sepia(81%) saturate(4907%) hue-rotate(223deg) brightness(99%) contrast(99%)"
};

/**
 * 工具函数：绘制变电站图标
 * @param xfmrMvarates 变压器容量数组
 * @returns 图标文件名
 */
const drawStationIcon = (xfmrMvarates: any[]): string => {
  // 这里应该根据实际业务逻辑实现
  // 暂时返回默认图标
  return "500KV.svg";
};

/**
 * 工具函数：获取电站图标
 * @param powerType 电站类型
 * @param powerTypeByCustom 自定义电站类型
 * @returns 图标文件名
 */
const getPowerStationIcon = (powerType: string, powerTypeByCustom: string | undefined): string => {
  if (powerType === "WIND") {
    const windType = powerTypeByCustom === "海" ? "WIND" : "LAND";
    return `powerPlant_s_${windType}.svg`;
  }
  return `powerPlant_s_${powerType}.svg`;
};

/**
 * 工具函数：获取变电站图标
 * @param volt 电压等级
 * @param stationType 站点类型
 * @param xfmrNo 变压器编号
 * @param metaData 元数据
 * @returns 图标文件名
 */
const getTransStationIcon = (
  volt: string | undefined,
  stationType: string | undefined,
  xfmrNo: string | undefined,
  metaData: MetaData
): string => {
  if (volt && HIGH_VOLT_LEVELS.includes(volt)) {
    const xfmrMvarates: any[] = [];

    if (xfmrNo) {
      xfmrNo.split(",").forEach((ele: string) => {
        const xfmrMvarate = `xfmrMvarate${ele}`;
        const xfmrRunningStatus = `xfmrRunningStatus${ele}`;
        const status = metaData[xfmrRunningStatus];

        if (status === "RUNNING") {
          xfmrMvarates.push(metaData[xfmrMvarate]);
        }
      });
    }

    return drawStationIcon(xfmrMvarates);
  }

  if (MEDIUM_VOLT_LEVELS.includes(volt || "")) {
    return stationType === "小电源" ? "220KVxdy.svg" : "220kV-01.svg";
  }

  return "";
};

/**
 * 工具函数：处理图片路径
 * @param img 原始图片路径
 * @returns 处理后的图片路径
 */
const processImagePath = (img: string): string => {
  if (img.startsWith("data:image") || img.startsWith("img/")) {
    return img;
  }

  return "/ftp/icon/" + img;
};

// 计算属性：节点样式
const nodeStyles = computed((): NodeStylesResult => {
  const style = { ...props.data.style };

  // 确保line属性存在
  if (!style.line) {
    style.line = {
      length: 0,
      rotate: 0
    };
  }

  // 处理动态颜色
  const filter = style.dynamicColor ? filterColorList[style.dynamicColor] : undefined;

  // 返回最终结果
  return {
    ...style,
    filter
  };
});

// 计算属性：图片URL
const imageUrl = computed((): string => {
  const { style, metaData: rawMetaData } = props.data;

  // 安全地处理metaData
  const metaData = (rawMetaData || {}) as MetaData;

  // 解构元数据
  const { powerType, powerTypeByCustom, volt, type, xfmrNo, stationType } = metaData;

  // 确定图标
  let img = "";

  if (type === "PowerStation" && powerType) {
    // 电站图标处理
    img = getPowerStationIcon(powerType, powerTypeByCustom);
  } else if (type === "TNode") {
    // T节点图标
    // img = require("../../assets/images/meta-icons/power/t.svg");
  } else if (type === "ExchStation") {
    // 交换站图标
    img = "powerPlant_s_ExchStation.svg";
  } else if (type === "TransStation") {
    // 变电站图标
    img = getTransStationIcon(volt, stationType, xfmrNo, metaData);
  }

  // 处理图片路径
  if (img) {
    img = processImagePath(img);
  } else {
    img = style.image || DefaultImage;
  }

  return img;
});
</script>
