import type {
  ICommand,
  IInteractionModel,
  IInteractionSource,
  IMapEventModel,
  IMapEventSource,
  IMapObjItem
} from "@/types";

import request from "../../index";

export const getMapEvent = (mapId: string, eventType?: string) => {
  return request.get<IMapEventSource[] | IInteractionSource[]>({
    url: "/topoEdit/getMapEvent",
    params: { mapId, eventType }
  });
};

export const addMapEvent = (params: IMapEventModel | IInteractionModel) => {
  return request.post<number>({
    url: "/topoEdit/insertMapEvent",
    data: params
  });
};

export const updateMapEvent = (params: IMapEventModel | IInteractionModel) => {
  return request.post({
    url: "/topoEdit/updateMapEvent",
    data: params
  });
};

export const deleteMapEvent = (id: number) => {
  return request.post({
    url: "/topoEdit/deleteMapEvent",
    data: { id }
  });
};

export const getObjDataListByMapId = (mapId: string) => {
  return request.get<IMapObjItem[]>({
    url: "/topoEdit/getObjDataListByMapId",
    params: { mapId }
  });
};

export const getControlCmdByResourceCode = (code: string) => {
  return request.get<ICommand[]>({
    url: "/config/sceneManager/getControlCmdByResourceCode",
    params: { code }
  });
};
