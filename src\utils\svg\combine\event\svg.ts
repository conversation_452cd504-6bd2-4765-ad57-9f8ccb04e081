import * as d3 from "d3";

import emitter from "@/utils/mitt";

export let groupTransform = d3.zoomIdentity;

export const bindCombineSvgZoom = () => {
  const zoom = d3.zoom<SVGSVGElement, any>().on("zoom", (event) => {
    groupTransform = event.transform;
    d3.select<SVGSVGElement, any>(".combine-group").attr("transform", event.transform);
  });
  d3.select<SVGSVGElement, any>("#combineSvg")
    .call(zoom)
    .on("click", function (event) {
      console.log(event.target);
      if (event.target === this) {
        emitter.emit("on:ClearCombineSelected");
      }
    });
};
