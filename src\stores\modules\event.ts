import { ref } from "vue";
import { defineStore } from "pinia";

import type { IInteractionSource, IMapEventSource, IMapObjItem } from "@/types";
import { getMapEvent, getObjDataListByMapId } from "@/utils/http/apis/";

export const useMapEventStore = defineStore("event", () => {
  const mapEventList = ref<IMapEventSource[]>([]);
  const mapObjList = ref<IMapObjItem[]>([]);
  const interactionList = ref<IInteractionSource[]>([]);

  const getMapEventList = async (mapId: string) => {
    const list = await getMapEvent(mapId);
    mapEventList.value = list.filter((item) => item.eventType === "filter") as IMapEventSource[];
    interactionList.value = list.filter(
      (item) => item.eventType === "interaction"
    ) as IInteractionSource[];
    return list;
  };

  const getObjDataList = async (mapId: string) => {
    const list = await getObjDataListByMapId(mapId);
    mapObjList.value = list;
    return list;
  };

  return {
    mapEventList,
    mapObjList,
    interactionList,
    getMapEventList,
    getObjDataList
  };
});
