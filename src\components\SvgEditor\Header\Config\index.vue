<template>
  <n-drawer v-model:show="show" :width="350" :show-mask="false" class="bg-#18181c">
    <n-drawer-content title="配置" closable>
      <BaseGrid title="分排线">
        <n-switch v-model:value="configStore.isPlanDividerVisible" size="small">
          <template #checked> 显示 </template>
          <template #unchecked> 隐藏 </template>
        </n-switch>
      </BaseGrid>

      <BaseGrid title="箭头">
        <n-switch v-model:value="mapStore.isLinkArrowVisible" size="small">
          <template #checked> 显示 </template>
          <template #unchecked> 隐藏 </template>
        </n-switch>
      </BaseGrid>
    </n-drawer-content>
  </n-drawer>

  <n-button quaternary size="small" class="mr-1" :disabled="!mapStore.mapInfo" @click="show = true">
    <n-icon size="20"><Settings /></n-icon>
  </n-button>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { Settings } from "@/utils/components/icons";
import { useMapStore, useConfigStore } from "@/stores";

import BaseGrid from "@/components/Common/BaseGrid/index.vue";

const mapStore = useMapStore();
const configStore = useConfigStore();

const show = ref(false);
</script>
