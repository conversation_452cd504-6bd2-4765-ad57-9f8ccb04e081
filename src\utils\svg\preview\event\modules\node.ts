import { useCommonStore, usePreviewStore } from "@/stores";
import type { INode, ISVGG } from "@/types";
import { sendSyncMessage } from "@/utils/sync";

import { highlightPreview } from "../../attr";
import { drawPreview<PERSON>hain } from "../../draw";
import { getChainNodesByNode } from "../../helper";

export const bindPreviewNodeEvent = (group: ISVGG<INode, SVGGElement>) => {
  const commonStore = useCommonStore();
  const previewStore = usePreviewStore();

  group
    .on("click", function (e, d) {
      highlightPreview(d);
      sendSyncMessage("onSelectNode", { type: "node", data: d.nodeId });
    })
    .on("dblclick", function (e, d) {
      const { sources } = getChainNodesByNode(d);
      drawPreviewChain(d, sources);
      sendSyncMessage("onShowPreviewChainModal", { type: "node", data: d.nodeId });
    })
    .on("contextmenu", function (e, d) {
      e.preventDefault();
      previewStore.isMenuVisible = true;
      previewStore.node = d;
      commonStore.mousePosition = { x: e.x, y: e.y };
    });
};
