import * as d3 from "d3";

import type { ILink, INode } from "@/types";

import { getChainLinksByNode } from "../../helper";
import { sendMessage } from "../../send";

export const FillColor = "#3c4a3d";
export const ActiveColor = "#eb6e00";
export const PathColor = "#aaaaaa";

export const highlightPreview = (d?: INode) => {
  d3.selectAll<SVGGElement, ILink>(".preview-link")
    .style("stroke", (d) => d.style.stroke)
    .attr("marker-end", "url(#arrow)");
  // d3.selectAll<SVGRectElement, INode>(".preview-node-path")
  //   .attr("fill", FillColor)
  //   .attr("stroke", FillColor);

  d3.selectAll<SVGRectElement, INode>(".preview-node-rect").style("fill", (d) => d.style.fill);

  if (d) {
    // d3.select(`#previewNode_${d.nodeId} > .preview-node-path`)
    //   .style("fill", ActiveColor)

    // d3.select(`#previewNode_${d.nodeId} > .preview-node-rect`).style("fill", ActiveColor);

    const { linkChains } = getChainLinksByNode(d);

    linkChains.forEach((link) => {
      d3.select<SVGPathElement, ILink>(`#previewLink_${link.linkId}`)
        .attr("marker-end", "url(#arrowActive)")
        .style("stroke", ActiveColor);

      if (link.source) {
        d3.select(`#previewNode_${link.source.nodeId} > .preview-node-rect`).style(
          "fill",
          ActiveColor
        );
      }
      if (link.target) {
        d3.select(`#previewNode_${link.target.nodeId} > .preview-node-rect`).style(
          "fill",
          ActiveColor
        );
      }
    });

    sendMessage(linkChains);
  } else {
    sendMessage();
  }
};
