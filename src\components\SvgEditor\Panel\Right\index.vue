<template>
  <n-card
    class="attribute-panel absolute top-5 right-0 w-90 h-90%"
    :style="translate"
    closable
    @close="closeAttributeView"
  >
    <div class="absolute top-0 left-0 w-full h-full p-2">
      <n-tabs v-model:value="tabActiveName" type="line" animated>
        <!-- #suffix 给card的close按钮提供空间显示  -->
        <template #suffix> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </template>
        <n-tab-pane name="attribute" tab="属性">
          <Attribute></Attribute>
        </n-tab-pane>
        <n-tab-pane
          name="layer"
          tab="子图层"
          :disabled="!mapStore.mapInfo"
          display-directive="show"
        >
          <SublayerList
        /></n-tab-pane>
      </n-tabs>
    </div>
  </n-card>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";

import { useCommonStore, useMapStore } from "@/stores/";
import { Close } from "@/utils/components/icons";

import Attribute from "./Attribute/index.vue";
import SublayerList from "./SublayerList/index.vue";

const commonStore = useCommonStore();
const mapStore = useMapStore();

const tabActiveName = ref("attribute");

const translate = computed(() => {
  return {
    transform:
      commonStore.isAttributeViewVisible && mapStore.mapInfo
        ? "translateX(-20px)"
        : "translateX(100%)"
  };
});

const closeAttributeView = () => {
  commonStore.isAttributeViewVisible = false;
};

watch(
  () => mapStore.mapInfo?.mapId,
  () => {
    tabActiveName.value = "attribute";
  }
);
</script>

<style>
.attribute-panel {
  transition: all 0.3s;
  background-color: #18181c;
}
.n-base-close {
  z-index: 99;
}
</style>
