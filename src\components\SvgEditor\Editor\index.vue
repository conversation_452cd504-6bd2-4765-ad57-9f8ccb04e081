<template>
  <n-dropdown
    trigger="manual"
    placement="bottom-start"
    :show="mapStore.isMenuVisible"
    :options="MenuOptions"
    :x="mapStore.menuePosition.x"
    :y="mapStore.menuePosition.y"
    @select="handleSelect"
    @clickoutside="() => (mapStore.isMenuVisible = false)"
    @contextmenu="($event: MouseEvent) => $event.preventDefault()"
  ></n-dropdown>

  <svg
    id="svgEditor"
    style="width: 100%; height: 100%"
    tabindex="0"
    @dragover="handleDragover"
    @drop="handleDrop"
  >
    <marker
      id="arrow"
      viewBox="0 0 10 10"
      refX="8"
      refY="5"
      markerWidth="6"
      markerHeight="6"
      orient="auto-start-reverse"
    >
      <path fill="#ccc" d="M 0 0 L 10 5 L 0 10 L 2 5 z" />
    </marker>

    <g id="map" :pointer-events="commonStore.isSpaceDown ? 'none' : 'auto'">
      <rect id="mapBackground" />
      <g v-if="svgStore.isBgSHow">
        <image
          v-for="(item, index) in bgUrls"
          :key="index"
          :href="item.url"
          :x="item.x"
          :y="item.y"
          :width="item.width"
          :height="item.heieght"
          preserveAspectRatio="none slice"
        />
      </g>

      <Container></Container>
      <DragAssistant></DragAssistant>
      <LinkDragPoint></LinkDragPoint>
      <LinkDraft></LinkDraft>
    </g>
    <rect id="selectionRect" v-show="dataStore.isSelectionRectVisible"></rect>
  </svg>

  <MoveToSublayerModal ref="moveToSublayerModalRef" />
  <RemoveSelectedFromSublayer ref="removeSelectedFromSublayerRef" />
  <AddGroup ref="addGroupRef" />
</template>

<script setup lang="ts">
import { computed, onMounted, watch } from "vue";

import LinkDraft from "@/components/SvgEditor/Editor/Assistant/LinkDraft.vue";
import Container from "@/components/SvgEditor/Editor/Container.vue";
import DragAssistant from "@/components/SvgEditor/Editor/DragAssistant.vue";
import LinkDragPoint from "@/components/SvgEditor/Editor/LinkDragPoint.vue";
import AddGroup from "@/components/SvgEditor/Modal/Group/Add.vue";
import MoveToSublayerModal from "@/components/SvgEditor/Modal/Sublayer/MoveToSublayer.vue";
import RemoveSelectedFromSublayer from "@/components/SvgEditor/Modal/Sublayer/RemoveSelectedFromSublayer.vue";
import { useSvgMenu } from "@/hooks/svg/useSvgMenu";
import { useCommonStore, useDataStore, useMapStore, useSvgStore } from "@/stores/";
import { EditMenu } from "@/utils/constant";
import { attrLinkDrag, attrNodeDrag, attrSvgDrag } from "@/utils/editor/attr";
import { attrSelectionDrag } from "@/utils/editor/attr/selection";
import { bindDragPointEvent, bindDragSelectionEvent } from "@/utils/editor/event/";
import { getImageUrl, onDroped, setNodesLinksSelected } from "@/utils/tools";

const dataStore = useDataStore();
const mapStore = useMapStore();
const svgStore = useSvgStore();
const commonStore = useCommonStore();

const { removeSelectedFromSublayerRef, moveToSublayerModalRef, addGroupRef, handleSelect } =
  useSvgMenu();

const MenuOptions = computed(() => {
  return EditMenu[mapStore.selectType];
});

const handleDragover = (e: DragEvent) => {
  e.preventDefault();
  e.dataTransfer && (e.dataTransfer.dropEffect = "copy");
};

const handleDrop = (e: DragEvent) => {
  e.preventDefault();
  onDroped(e);
};

const bgUrls = computed(() => {
  const preUrl = getImageUrl();
  //   if (svgStore.bgType === "local") return svgStore.bgUrl;
  const bgs = mapStore.mapInfo?.background.split(",") || [];
  const len = bgs.length;
  return bgs.map((item, index) => {
    const width = mapStore.mapSize.width / len;
    return {
      url: item.includes("http") ? item : preUrl + item,
      width,
      heieght: mapStore.mapSize.height,
      x: width * index,
      y: 0
    };
  });
});

onMounted(() => {
  bindDragPointEvent();
  bindDragSelectionEvent();
});

watch(
  () => commonStore.isSpaceDown,
  (val) => {
    attrLinkDrag(val);
    attrNodeDrag(val);
    attrSvgDrag(val);
    attrSelectionDrag(!val);
  }
);

watch(
  () => commonStore.isCtrlADown,
  (val) => {
    val && setNodesLinksSelected();
  }
);
</script>
