<template>
  <BaseGrid title="位置">
    <n-flex :wrap="false">
      <n-input-number v-model:value="positionScale" size="small" :min="0.01" :step="0.1" />
      <!-- <n-button size="small" @click="preview">预览</n-button>
      <n-button size="small" @click="reset">重置</n-button> -->
      <n-button type="primary" size="small" @click="saveData">设置</n-button>
    </n-flex>
  </BaseGrid>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { SVGPathData } from "svg-pathdata";
import * as d3 from "d3";
import BaseGrid from "@/components/Common/BaseGrid/index.vue";

import { useDataStore, useMapStore } from "@/stores";
import { attrMapBackground } from "@/utils/editor/attr";
import { updateMap, updateNodesLinks } from "@/utils/http/apis";

const mapStore = useMapStore();
const dataStore = useDataStore();

const positionScale = ref(1);

const preview = () => {
  d3.select("#map");
};

const reset = () => {};

const saveData = async () => {
  mapStore.mapSize.width = mapStore.mapSize.width * positionScale.value;
  mapStore.mapSize.height = mapStore.mapSize.height * positionScale.value;

  mapStore.mapInfo!.width = mapStore.mapSize.width;
  mapStore.mapInfo!.height = mapStore.mapSize.height;
  mapStore.mapInfo!.mapSize = `${mapStore.mapSize.width}*${mapStore.mapSize.height}`;

  dataStore.nodesAll.forEach((node) => {
    node.x = node.x * positionScale.value;
    node.y = node.y * positionScale.value;
    node.width = node.width * positionScale.value;
    node.height = node.height * positionScale.value;

    node.nodePosition = `${node.x},${node.y}`;
    node.nodeSize = `${node.width}*${node.height}`;
  });

  dataStore.linksAll.forEach((link) => {
    // 将link.points中的每个点都乘以sizeScale.value
    link.linkPath = new SVGPathData(link.linkPath).scale(positionScale.value).toAbs().encode();
    link.points = new SVGPathData(link.linkPath).toAbs().commands;
  });

  attrMapBackground();
  positionScale.value = 1;

  await updateMap(mapStore.mapInfo!);
  updateNodesLinks({
    nodes: dataStore.nodesAll,
    links: dataStore.linksAll
  });
};
</script>

<style scoped></style>
