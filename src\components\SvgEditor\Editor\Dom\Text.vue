<template>
  <text :width="data.width" :height="data.height" :style="style">
    <tspan
      v-for="(line, index) in nodeText"
      :key="index"
      :x="0"
      :dy="index === 0 ? '0' : '1.2em'"
      alignment-baseline="hanging"
    >
      {{ line }}
    </tspan>
  </text>
</template>

<script setup lang="ts">
import { computed } from "vue";

import type { INode } from "@/types";

const props = defineProps<{
  data: INode;
}>();

/**
 * 如果有换行,返回数组 并显示
 */
const nodeText = computed(() => {
  const text = props.data.nodeText;
  if (typeof text === "string" && text.includes("\n")) {
    return text.split("\n");
  }
  return [text || ""];
});

const style = computed(() => {
  const { textStyle } = props.data;
  return {
    ...textStyle,
    fill: props.data.fontColor,
    fontSize: props.data.fontSize + "px"
  };
});
</script>

<style></style>
